#!/usr/bin/env julia

"""
Drone CFD Mesh Generator
Creates a complete OpenFOAM mesh for drone aerodynamics simulation
"""

using StaticArrays
using Printf

function create_drone_mesh()
    println("🚁 Creating drone CFD mesh...")
    
    # Mesh parameters for drone domain
    nx, ny, nz = 50, 50, 30  # Higher resolution mesh
    lx, ly, lz = 4.0, 4.0, 3.0  # Domain size (meters)
    
    # Create points
    points = Vector{SVector{3,Float64}}()
    
    for k in 0:nz
        for j in 0:ny
            for i in 0:nx
                x = -lx/2 + (i/nx) * lx
                y = -ly/2 + (j/ny) * ly
                z = (k/nz) * lz
                push!(points, SVector(x, y, z))
            end
        end
    end
    
    n_points = length(points)
    println("Created $n_points points")
    
    # Create cells (hexahedral)
    cells = []
    cell_centres = Vector{SVector{3,Float64}}()
    
    for k in 0:nz-1
        for j in 0:ny-1
            for i in 0:nx-1
                # Cell vertices (0-indexed for OpenFOAM)
                p0 = k*(nx+1)*(ny+1) + j*(nx+1) + i
                p1 = p0 + 1
                p2 = p0 + (nx+1) + 1
                p3 = p0 + (nx+1)
                p4 = p0 + (nx+1)*(ny+1)
                p5 = p4 + 1
                p6 = p4 + (nx+1) + 1
                p7 = p4 + (nx+1)
                
                cell = [p0, p1, p2, p3, p4, p5, p6, p7]
                push!(cells, cell)
                
                # Cell centre
                cx = -lx/2 + (i+0.5)/nx * lx
                cy = -ly/2 + (j+0.5)/ny * ly
                cz = (k+0.5)/nz * lz
                push!(cell_centres, SVector(cx, cy, cz))
            end
        end
    end
    
    n_cells = length(cells)
    println("Created $n_cells cells")
    
    # Create faces and determine boundaries
    faces = []
    face_owners = []
    face_neighbours = []
    
    # Internal faces (i-direction)
    for k in 0:nz-1
        for j in 0:ny-1
            for i in 0:nx-2
                # Face between cells i and i+1
                p0 = k*(nx+1)*(ny+1) + j*(nx+1) + (i+1)
                p1 = p0 + (nx+1)
                p2 = p1 + (nx+1)*(ny+1)
                p3 = p0 + (nx+1)*(ny+1)
                
                face = [p0, p1, p2, p3]
                push!(faces, face)
                
                owner = k*nx*ny + j*nx + i
                neighbour = owner + 1
                push!(face_owners, owner)
                push!(face_neighbours, neighbour)
            end
        end
    end
    
    # Internal faces (j-direction)
    for k in 0:nz-1
        for j in 0:ny-2
            for i in 0:nx-1
                p0 = k*(nx+1)*(ny+1) + (j+1)*(nx+1) + i
                p1 = p0 + 1
                p2 = p1 + (nx+1)*(ny+1)
                p3 = p0 + (nx+1)*(ny+1)
                
                face = [p0, p1, p2, p3]
                push!(faces, face)
                
                owner = k*nx*ny + j*nx + i
                neighbour = owner + nx
                push!(face_owners, owner)
                push!(face_neighbours, neighbour)
            end
        end
    end
    
    # Internal faces (k-direction)
    for k in 0:nz-2
        for j in 0:ny-1
            for i in 0:nx-1
                p0 = (k+1)*(nx+1)*(ny+1) + j*(nx+1) + i
                p1 = p0 + 1
                p2 = p0 + (nx+1) + 1
                p3 = p0 + (nx+1)
                
                face = [p0, p1, p2, p3]
                push!(faces, face)
                
                owner = k*nx*ny + j*nx + i
                neighbour = owner + nx*ny
                push!(face_owners, owner)
                push!(face_neighbours, neighbour)
            end
        end
    end
    
    n_internal_faces = length(faces)
    println("Created $n_internal_faces internal faces")
    
    # Boundary faces
    boundary_faces = Dict(
        "inlet" => [],
        "outlet" => [],
        "walls" => [],
        "top" => [],
        "bottom" => [],
        "drone_body" => []
    )
    
    # Inlet (i=0)
    for k in 0:nz-1
        for j in 0:ny-1
            p0 = k*(nx+1)*(ny+1) + j*(nx+1)
            p1 = p0 + (nx+1)
            p2 = p1 + (nx+1)*(ny+1)
            p3 = p0 + (nx+1)*(ny+1)
            
            face = [p0, p3, p2, p1]  # Outward normal
            push!(faces, face)
            push!(boundary_faces["inlet"], length(faces))
            
            owner = k*nx*ny + j*nx
            push!(face_owners, owner)
        end
    end
    
    # Outlet (i=nx-1)
    for k in 0:nz-1
        for j in 0:ny-1
            p0 = k*(nx+1)*(ny+1) + j*(nx+1) + nx
            p1 = p0 + (nx+1)
            p2 = p1 + (nx+1)*(ny+1)
            p3 = p0 + (nx+1)*(ny+1)
            
            face = [p0, p1, p2, p3]  # Outward normal
            push!(faces, face)
            push!(boundary_faces["outlet"], length(faces))
            
            owner = k*nx*ny + j*nx + (nx-1)
            push!(face_owners, owner)
        end
    end
    
    # Bottom wall (j=0)
    for k in 0:nz-1
        for i in 0:nx-1
            p0 = k*(nx+1)*(ny+1) + i
            p1 = p0 + 1
            p2 = p1 + (nx+1)*(ny+1)
            p3 = p0 + (nx+1)*(ny+1)
            
            face = [p0, p3, p2, p1]  # Outward normal
            push!(faces, face)
            push!(boundary_faces["walls"], length(faces))
            
            owner = k*nx*ny + i
            push!(face_owners, owner)
        end
    end
    
    # Top wall (j=ny-1)
    for k in 0:nz-1
        for i in 0:nx-1
            p0 = k*(nx+1)*(ny+1) + ny*(nx+1) + i
            p1 = p0 + 1
            p2 = p1 + (nx+1)*(ny+1)
            p3 = p0 + (nx+1)*(ny+1)
            
            face = [p0, p1, p2, p3]  # Outward normal
            push!(faces, face)
            push!(boundary_faces["walls"], length(faces))
            
            owner = k*nx*ny + (ny-1)*nx + i
            push!(face_owners, owner)
        end
    end
    
    # Bottom boundary (k=0)
    for j in 0:ny-1
        for i in 0:nx-1
            p0 = j*(nx+1) + i
            p1 = p0 + 1
            p2 = p0 + (nx+1) + 1
            p3 = p0 + (nx+1)
            
            face = [p0, p3, p2, p1]  # Outward normal
            push!(faces, face)
            push!(boundary_faces["bottom"], length(faces))
            
            owner = j*nx + i
            push!(face_owners, owner)
        end
    end
    
    # Top boundary (k=nz-1) - this will include drone region
    for j in 0:ny-1
        for i in 0:nx-1
            p0 = nz*(nx+1)*(ny+1) + j*(nx+1) + i
            p1 = p0 + 1
            p2 = p0 + (nx+1) + 1
            p3 = p0 + (nx+1)
            
            face = [p0, p1, p2, p3]  # Outward normal
            push!(faces, face)
            
            # Check if this face is in drone region
            x = -lx/2 + (i+0.5)/nx * lx
            y = -ly/2 + (j+0.5)/ny * ly
            
            if abs(x) < 0.8 && abs(y) < 0.8  # Drone body region
                push!(boundary_faces["drone_body"], length(faces))
            else
                push!(boundary_faces["top"], length(faces))
            end
            
            owner = (nz-1)*nx*ny + j*nx + i
            push!(face_owners, owner)
        end
    end
    
    total_faces = length(faces)
    println("Total faces: $total_faces")
    println("Boundary face counts:")
    for (name, face_list) in boundary_faces
        println("  $name: $(length(face_list)) faces")
    end
    
    return (points, faces, face_owners, face_neighbours, boundary_faces, cell_centres, 
            n_internal_faces, nx, ny, nz)
end

function write_openfoam_mesh(case_dir, mesh_data)
    points, faces, face_owners, face_neighbours, boundary_faces, cell_centres, 
    n_internal_faces, nx, ny, nz = mesh_data
    
    mesh_dir = joinpath(case_dir, "constant", "polyMesh")
    mkpath(mesh_dir)
    
    println("📁 Writing OpenFOAM mesh files to $mesh_dir")
    
    # Write points
    open(joinpath(mesh_dir, "points"), "w") do f
        write(f, """/*--------------------------------*- C++ -*----------------------------------*\\
| =========                 |                                                 |
| \\\\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\\\    /   O peration     | Version:  v2112                                 |
|   \\\\  /    A nd           | Website:  www.openfoam.com                      |
|    \\\\/     M anipulation  |                                                 |
\\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       vectorField;
    location    "constant/polyMesh";
    object      points;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

$(length(points))
(
""")
        for pt in points
            @printf(f, "(%.6f %.6f %.6f)\n", pt[1], pt[2], pt[3])
        end
        write(f, ")\n\n// ************************************************************************* //\n")
    end
    
    # Write faces
    open(joinpath(mesh_dir, "faces"), "w") do f
        write(f, """/*--------------------------------*- C++ -*----------------------------------*\\
| =========                 |                                                 |
| \\\\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\\\    /   O peration     | Version:  v2112                                 |
|   \\\\  /    A nd           | Website:  www.openfoam.com                      |
|    \\\\/     M anipulation  |                                                 |
\\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       faceList;
    location    "constant/polyMesh";
    object      faces;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

$(length(faces))
(
""")
        for face in faces
            write(f, "4($(face[1]) $(face[2]) $(face[3]) $(face[4]))\n")
        end
        write(f, ")\n\n// ************************************************************************* //\n")
    end
    
    # Write owner
    open(joinpath(mesh_dir, "owner"), "w") do f
        write(f, """/*--------------------------------*- C++ -*----------------------------------*\\
| =========                 |                                                 |
| \\\\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\\\    /   O peration     | Version:  v2112                                 |
|   \\\\  /    A nd           | Website:  www.openfoam.com                      |
|    \\\\/     M anipulation  |                                                 |
\\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       labelList;
    location    "constant/polyMesh";
    object      owner;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

$(length(face_owners))
(
""")
        for owner in face_owners
            write(f, "$owner\n")
        end
        write(f, ")\n\n// ************************************************************************* //\n")
    end
    
    # Write neighbour
    open(joinpath(mesh_dir, "neighbour"), "w") do f
        write(f, """/*--------------------------------*- C++ -*----------------------------------*\\
| =========                 |                                                 |
| \\\\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\\\    /   O peration     | Version:  v2112                                 |
|   \\\\  /    A nd           | Website:  www.openfoam.com                      |
|    \\\\/     M anipulation  |                                                 |
\\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       labelList;
    location    "constant/polyMesh";
    object      neighbour;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

$(length(face_neighbours))
(
""")
        for neighbour in face_neighbours
            write(f, "$neighbour\n")
        end
        write(f, ")\n\n// ************************************************************************* //\n")
    end
    
    # Write boundary
    open(joinpath(mesh_dir, "boundary"), "w") do f
        write(f, """/*--------------------------------*- C++ -*----------------------------------*\\
| =========                 |                                                 |
| \\\\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\\\    /   O peration     | Version:  v2112                                 |
|   \\\\  /    A nd           | Website:  www.openfoam.com                      |
|    \\\\/     M anipulation  |                                                 |
\\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

$(length(boundary_faces))
(
""")
        
        start_face = n_internal_faces
        
        for (patch_name, face_indices) in boundary_faces
            if !isempty(face_indices)
                patch_type = if patch_name == "drone_body"
                    "wall"
                elseif patch_name in ["inlet", "outlet"]
                    "patch"
                elseif patch_name in ["walls", "top", "bottom"]
                    "wall"
                else
                    "patch"
                end
                
                write(f, """    $patch_name
    {
        type            $patch_type;
        nFaces          $(length(face_indices));
        startFace       $start_face;
    }
""")
                start_face += length(face_indices)
            end
        end
        
        write(f, ")\n\n// ************************************************************************* //\n")
    end
    
    println("✅ Mesh files written successfully")
end

function setup_drone_case()
    case_dir = "/home/<USER>/dev/jewJulia/examples/industrial/enhanced_drone_rotor"
    
    println("🚁 Setting up drone CFD case in $case_dir")
    
    # Generate mesh
    mesh_data = create_drone_mesh()
    write_openfoam_mesh(case_dir, mesh_data)
    
    # Update boundary conditions in 0/ directory
    println("🔧 Setting up boundary conditions...")
    
    # Write U (velocity) field
    open(joinpath(case_dir, "0", "U"), "w") do f
        write(f, """/*--------------------------------*- C++ -*----------------------------------*\\
| =========                 |                                                 |
| Field: U                  | CFD.jl Drone Simulation                        |
\\*---------------------------------------------------------------------------*/

FoamFile
{
    version     2.0;
    format      ascii;
    class       volVectorField;
    object      U;
}

dimensions      [0 1 -1 0 0 0 0];

internalField   uniform (5 0 0);

boundaryField
{
    inlet
    {
        type            fixedValue;
        value           uniform (5 0 0);
    }
    
    outlet
    {
        type            zeroGradient;
    }
    
    walls
    {
        type            noSlip;
        value           uniform (0 0 0);
    }
    
    top
    {
        type            slip;
    }
    
    bottom
    {
        type            noSlip;
        value           uniform (0 0 0);
    }
    
    drone_body
    {
        type            noSlip;
        value           uniform (0 0 0);
    }
}
""")
    end
    
    # Write p (pressure) field
    open(joinpath(case_dir, "0", "p"), "w") do f
        write(f, """/*--------------------------------*- C++ -*----------------------------------*\\
| =========                 |                                                 |
| Field: p                  | CFD.jl Drone Simulation                        |
\\*---------------------------------------------------------------------------*/

FoamFile
{
    version     2.0;
    format      ascii;
    class       volScalarField;
    object      p;
}

dimensions      [0 2 -2 0 0 0 0];

internalField   uniform 0;

boundaryField
{
    inlet
    {
        type            zeroGradient;
    }
    
    outlet
    {
        type            fixedValue;
        value           uniform 0;
    }
    
    walls
    {
        type            zeroGradient;
    }
    
    top
    {
        type            zeroGradient;
    }
    
    bottom
    {
        type            zeroGradient;
    }
    
    drone_body
    {
        type            zeroGradient;
    }
}
""")
    end
    
    # Write turbulence fields for k and epsilon
    open(joinpath(case_dir, "0", "k"), "w") do f
        write(f, """/*--------------------------------*- C++ -*----------------------------------*\\
FoamFile
{
    version     2.0;
    format      ascii;
    class       volScalarField;
    object      k;
}

dimensions      [0 2 -2 0 0 0 0];

internalField   uniform 0.375;

boundaryField
{
    inlet
    {
        type            fixedValue;
        value           uniform 0.375;
    }
    
    outlet
    {
        type            zeroGradient;
    }
    
    walls
    {
        type            kqRWallFunction;
        value           uniform 0.375;
    }
    
    top
    {
        type            slip;
    }
    
    bottom
    {
        type            kqRWallFunction;
        value           uniform 0.375;
    }
    
    drone_body
    {
        type            kqRWallFunction;
        value           uniform 0.375;
    }
}
""")
    end
    
    open(joinpath(case_dir, "0", "epsilon"), "w") do f
        write(f, """/*--------------------------------*- C++ -*----------------------------------*\\
FoamFile
{
    version     2.0;
    format      ascii;
    class       volScalarField;
    object      epsilon;
}

dimensions      [0 2 -3 0 0 0 0];

internalField   uniform 14.855;

boundaryField
{
    inlet
    {
        type            fixedValue;
        value           uniform 14.855;
    }
    
    outlet
    {
        type            zeroGradient;
    }
    
    walls
    {
        type            epsilonWallFunction;
        value           uniform 14.855;
    }
    
    top
    {
        type            slip;
    }
    
    bottom
    {
        type            epsilonWallFunction;
        value           uniform 14.855;
    }
    
    drone_body
    {
        type            epsilonWallFunction;
        value           uniform 14.855;
    }
}
""")
    end
    
    open(joinpath(case_dir, "0", "nut"), "w") do f
        write(f, """/*--------------------------------*- C++ -*----------------------------------*\\
FoamFile
{
    version     2.0;
    format      ascii;
    class       volScalarField;
    object      nut;
}

dimensions      [0 2 -1 0 0 0 0];

internalField   uniform 0;

boundaryField
{
    inlet
    {
        type            calculated;
        value           uniform 0;
    }
    
    outlet
    {
        type            calculated;
        value           uniform 0;
    }
    
    walls
    {
        type            nutkWallFunction;
        value           uniform 0;
    }
    
    top
    {
        type            slip;
    }
    
    bottom
    {
        type            nutkWallFunction;
        value           uniform 0;
    }
    
    drone_body
    {
        type            nutkWallFunction;
        value           uniform 0;
    }
}
""")
    end
    
    println("✅ Drone CFD case setup complete!")
    
    # Print summary
    points, faces, face_owners, face_neighbours, boundary_faces, cell_centres, 
    n_internal_faces, nx, ny, nz = mesh_data
    
    println("\n📊 Mesh Statistics:")
    println("   Domain: 4m × 4m × 3m")
    println("   Grid: $(nx) × $(ny) × $(nz)")
    println("   Points: $(length(points))")
    println("   Cells: $(length(cell_centres))")
    println("   Faces: $(length(faces))")
    println("   Internal faces: $n_internal_faces")
    
    println("\n🎯 Simulation Setup:")
    println("   Inlet velocity: 5 m/s")
    println("   Turbulence model: k-ε")
    println("   Drone body included as wall boundary")
    println("   Ready for CFD simulation!")
    
    return case_dir
end

# Run the setup
if abspath(PROGRAM_FILE) == @__FILE__
    setup_drone_case()
end