#!/usr/bin/env julia

"""
Test NaN Fixes
"""

using Pkg
Pkg.activate(".")

using CFD.MinimalCFD: read_mesh, auto_mesh, set_bc!, PISO, 𝐮, φ
using CFD.MinimalCFD: calculate_residuals, predictor_step!, corrector_step!
using CFD.MinimalCFD: apply_boundary_conditions!
using StaticArrays
using LinearAlgebra
using Printf

println("🔧 Testing NaN Fixes")
println("="^30)

function test_nan_fixes()
    # Create test case
    mesh_path = auto_mesh("test_nan_fix", (6, 6, 1))
    mesh = read_mesh(mesh_path)
    
    println("✅ Mesh created: $(mesh.ncells) cells")
    
    # Create fields
    U = 𝐮(:U, mesh)
    p = φ(:p, mesh)
    solver = PISO(mesh)
    
    # Set boundary conditions
    set_bc!(U, :inlet, (0.5, 0.0, 0.0))
    set_bc!(U, :wall, (0.0, 0.0, 0.0))
    set_bc!(U, :outlet, (0.1, 0.0, 0.0))
    set_bc!(p, :outlet, 0.0)
    set_bc!(p, :wall, "zeroGradient")
    set_bc!(p, :inlet, "zeroGradient")
    
    println("✅ Boundary conditions set")
    
    dt = 0.005
    max_steps = 10
    nan_detected = false
    
    for step in 1:max_steps
        try
            # Calculate residuals
            residuals = calculate_residuals(U, p, step)
            
            # Check for NaN
            if any(isnan.(residuals)) || any(isinf.(residuals))
                @printf "  ❌ Step %d: NaN/Inf detected in residuals: [%.2e, %.2e, %.2e]\n" step residuals[1] residuals[2] residuals[3]
                nan_detected = true
                break
            else
                @printf "  ✅ Step %d: Residuals [%.2e, %.2e, %.2e]\n" step residuals[1] residuals[2] residuals[3]
            end
            
            # PISO algorithm
            predictor_step!(U, p, dt, solver)
            corrector_step!(U, p, dt, solver, 1)
            
            # Apply boundary conditions
            apply_boundary_conditions!(U)
            apply_boundary_conditions!(p)
            
            # Check field values for NaN
            if any(x -> any(isnan.(x)), U.data) || any(isnan, p.data)
                println("  ❌ Step $step: NaN detected in field values")
                nan_detected = true
                break
            end
            
        catch e
            println("  ❌ Step $step failed: $e")
            break
        end
    end
    
    if !nan_detected
        println("\n🎉 SUCCESS: No NaN values detected!")
        println("  • All residuals remain finite")
        println("  • PISO algorithm stable")
        println("  • Field values remain finite")
    else
        println("\n⚠️  NaN issues still present")
    end
    
    return !nan_detected
end

# Run the test
success = test_nan_fixes()

if success
    println("\n✅ NaN fixes SUCCESSFUL!")
else
    println("\n❌ NaN issues still need attention")
end