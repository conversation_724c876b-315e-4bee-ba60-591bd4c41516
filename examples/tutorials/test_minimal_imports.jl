using Pkg
Pkg.activate(".")

using CFD

# Test basic imports
println("Testing CFD imports...")

try
    using CFD.MinimalCFD
    println("✅ MinimalCFD imported")
    
    # Test function availability
    functions_to_test = [:read_mesh, :auto_mesh, :set_bc!, :solve!, :PISO]
    
    for func in functions_to_test
        if isdefined(CFD.MinimalCFD, func)
            println("✅ $func available")
        else
            println("❌ $func not available")
        end
    end
    
catch e
    println("❌ Import failed: $e")
end