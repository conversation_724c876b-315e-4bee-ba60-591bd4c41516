#!/usr/bin/env julia

"""
Test Boundary Conditions Fixes
"""

using Pkg
Pkg.activate(".")

using CFD.MinimalCFD: read_mesh, auto_mesh, set_bc!, 𝐮, φ
using CFD.MinimalCFD: apply_boundary_conditions!, get_boundary_cells
using StaticArrays
using LinearAlgebra
using Printf

println("🔧 Testing Fixed Boundary Conditions")
println("="^40)

function test_boundary_condition_fixes()
    # Create test case
    mesh_path = auto_mesh("test_bc_fix", (6, 6, 1))
    mesh = read_mesh(mesh_path)
    
    println("✅ Mesh created: $(mesh.ncells) cells")
    
    # Create fields
    U = 𝐮(:U, mesh)
    p = φ(:p, mesh)
    
    println("✅ Fields created: U=$(length(U.data)) cells, p=$(length(p.data)) cells")
    
    # Set initial values (non-zero to verify BC application)
    for i in eachindex(U.data)
        U.data[i] = SVector(0.5, 0.3, 0.0)
    end
    for i in eachindex(p.data)
        p.data[i] = 100.0
    end
    
    println("✅ Initial values set")
    
    # Set boundary conditions
    set_bc!(U, :inlet, (2.0, 0.0, 0.0))
    set_bc!(U, :wall, (0.0, 0.0, 0.0))
    set_bc!(U, :outlet, (1.0, 0.0, 0.0))
    set_bc!(p, :outlet, 0.0)
    set_bc!(p, :wall, "zeroGradient")
    set_bc!(p, :inlet, "zeroGradient")
    
    println("✅ Boundary conditions set")
    
    # Test boundary cell identification
    inlet_cells = get_boundary_cells(mesh, "inlet")
    outlet_cells = get_boundary_cells(mesh, "outlet") 
    wall_cells = get_boundary_cells(mesh, "wall")
    
    @printf "  Inlet cells: %s\n" inlet_cells
    @printf "  Outlet cells: %s\n" outlet_cells
    @printf "  Wall cells: %s\n" wall_cells
    
    # Apply boundary conditions
    apply_boundary_conditions!(U)
    apply_boundary_conditions!(p)
    
    println("✅ Boundary conditions applied")
    
    # Verify boundary conditions
    bc_correct = true
    bc_issues = String[]
    
    # Check inlet velocity
    if !isempty(inlet_cells)
        for cell_id in inlet_cells[1:min(3, length(inlet_cells))]  # Check first few
            if cell_id <= length(U.data)
                inlet_vel = U.data[cell_id]
                if abs(inlet_vel[1] - 2.0) > 1e-10 || abs(inlet_vel[2]) > 1e-10
                    push!(bc_issues, "Inlet velocity not set correctly at cell $cell_id: $(inlet_vel)")
                    bc_correct = false
                end
            end
        end
    else
        push!(bc_issues, "No inlet cells found")
        bc_correct = false
    end
    
    # Check wall velocity (should be zero)
    if !isempty(wall_cells)
        for cell_id in wall_cells[1:min(3, length(wall_cells))]  # Check first few
            if cell_id <= length(U.data)
                wall_vel = U.data[cell_id]
                if norm(wall_vel) > 1e-10
                    push!(bc_issues, "Wall velocity not zero at cell $cell_id: $(wall_vel)")
                    bc_correct = false
                end
            end
        end
    else
        push!(bc_issues, "No wall cells found")
        bc_correct = false
    end
    
    # Check outlet pressure
    if !isempty(outlet_cells)
        for cell_id in outlet_cells[1:min(3, length(outlet_cells))]  # Check first few
            if cell_id <= length(p.data)
                outlet_pressure = p.data[cell_id]
                if abs(outlet_pressure) > 1e-10
                    push!(bc_issues, "Outlet pressure not zero at cell $cell_id: $(outlet_pressure)")
                    bc_correct = false
                end
            end
        end
    else
        push!(bc_issues, "No outlet cells found")
        bc_correct = false
    end
    
    # Report results
    if bc_correct
        println("✅ ALL BOUNDARY CONDITIONS CORRECTLY APPLIED!")
        println("  • Inlet velocity: (2.0, 0.0, 0.0) ✓")
        println("  • Wall velocity: (0.0, 0.0, 0.0) ✓")
        println("  • Outlet pressure: 0.0 ✓")
        println("  • Zero gradient conditions applied ✓")
    else
        println("❌ BOUNDARY CONDITION ISSUES DETECTED:")
        for issue in bc_issues
            println("    • $issue")
        end
    end
    
    return bc_correct
end

# Run the test
success = test_boundary_condition_fixes()

if success
    println("\n🎉 Boundary condition fixes SUCCESSFUL!")
else
    println("\n⚠️  Boundary condition issues still present")
end