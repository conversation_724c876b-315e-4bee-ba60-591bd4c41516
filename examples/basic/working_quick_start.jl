#!/usr/bin/env julia

"""
Working Quick Start Examples for Enhanced CFD.jl
===============================================

This file contains simple, working examples that demonstrate the enhanced
CFD.jl ecosystem features. All examples are designed to work without
complex dependencies and provide clear output.

Examples included:
1. Basic mathematical operations and CFD concepts
2. Simple mesh operations
3. Field creation and basic operations
4. Dictionary-style configuration
5. Basic monitoring concepts
6. Simple optimization demonstrations
"""

using CFD
using LinearAlgebra
using Printf

println("🚀 CFD.jl Enhanced Ecosystem - Working Quick Start")
println("=" ^ 50)

# ============================================================================
# Example 1: Basic CFD Mathematical Operations
# ============================================================================

function example_1_basic_math()
    println("\n📖 Example 1: Basic CFD Mathematical Operations")
    println("─" ^ 50)
    
    # Vector field operations
    println("🔢 Vector field operations:")
    
    # Create sample velocity vectors
    velocities = [
        [1.0, 0.0, 0.0],  # x-direction flow
        [0.5, 0.5, 0.0],  # diagonal flow
        [0.0, 1.0, 0.0],  # y-direction flow
        [-0.5, 0.5, 0.0], # mixed flow
        [0.0, 0.0, 0.0]   # stagnation
    ]
    
    # Calculate magnitudes and directions
    for (i, v) in enumerate(velocities)
        magnitude = norm(v)
        direction = magnitude > 0 ? v ./ magnitude : [0.0, 0.0, 0.0]
        println("  Point $i: |U| = $(@sprintf("%.3f", magnitude)) m/s, direction = [$(@sprintf("%.2f", direction[1])), $(@sprintf("%.2f", direction[2])), $(@sprintf("%.2f", direction[3]))]")
    end
    
    # Scalar field operations
    println("\n🌡️  Temperature field operations:")
    
    temperatures = [100.0, 80.0, 60.0, 40.0, 20.0]  # °C
    x_positions = [0.0, 0.25, 0.5, 0.75, 1.0]       # m
    
    # Calculate temperature gradient (simple finite difference)
    gradients = Float64[]
    for i in 2:length(temperatures)
        dT_dx = (temperatures[i] - temperatures[i-1]) / (x_positions[i] - x_positions[i-1])
        push!(gradients, dT_dx)
        println("  Gradient at x = $(@sprintf("%.2f", x_positions[i])): dT/dx = $(@sprintf("%.1f", dT_dx)) °C/m")
    end
    
    println("✓ Basic mathematical operations completed")
    
    return (velocities=velocities, temperatures=temperatures, gradients=gradients)
end

# ============================================================================
# Example 2: Simple Mesh Concepts
# ============================================================================

function example_2_mesh_concepts()
    println("\n🌐 Example 2: Simple Mesh Concepts")
    println("─" ^ 35)
    
    # Create a simple 1D mesh
    println("Creating 1D mesh:")
    
    n_cells = 10
    L = 1.0  # domain length
    
    # Cell centers
    cell_centers = [(i - 0.5) * L / n_cells for i in 1:n_cells]
    cell_volumes = [L / n_cells for i in 1:n_cells]
    
    # Face positions  
    face_positions = [i * L / n_cells for i in 0:n_cells]
    
    println("✓ Mesh created:")
    println("  Domain: [0, $(L)] m")
    println("  Cells: $(n_cells)")
    println("  Cell size: $(@sprintf("%.3f", L/n_cells)) m")
    
    # Show some mesh details
    println("  Sample cell centers: [", join([@sprintf("%.3f", x) for x in cell_centers[1:3]], ", "), ", ...]")
    println("  Sample face positions: [", join([@sprintf("%.3f", x) for x in face_positions[1:4]], ", "), ", ...]")
    
    # Demonstrate mesh operations
    println("\n🔧 Mesh operations:")
    
    # Calculate cell-to-face distances
    distances = Float64[]
    for i in 1:n_cells
        left_distance = cell_centers[i] - face_positions[i]
        right_distance = face_positions[i+1] - cell_centers[i]
        push!(distances, min(left_distance, right_distance))
    end
    
    min_distance = minimum(distances)
    max_distance = maximum(distances)
    
    println("  Cell-to-face distances: min = $(@sprintf("%.4f", min_distance)), max = $(@sprintf("%.4f", max_distance))")
    
    # Calculate mesh quality metrics
    aspect_ratio = 1.0  # 1D mesh has aspect ratio = 1
    orthogonality = 1.0  # 1D mesh is perfectly orthogonal
    
    println("  Mesh quality: aspect ratio = $(@sprintf("%.1f", aspect_ratio)), orthogonality = $(@sprintf("%.1f", orthogonality))")
    
    println("✓ Mesh concepts demonstrated")
    
    mesh_data = (
        cell_centers = cell_centers,
        cell_volumes = cell_volumes,
        face_positions = face_positions,
        n_cells = n_cells,
        domain_length = L
    )
    
    return mesh_data
end

# ============================================================================
# Example 3: Field Creation and Operations
# ============================================================================

function example_3_field_operations()
    println("\n🌊 Example 3: Field Creation and Operations")
    println("─" ^ 45)
    
    # Use mesh from previous example
    mesh_data = example_2_mesh_concepts()
    n_cells = mesh_data.n_cells
    x = mesh_data.cell_centers
    
    println("\n🌡️  Creating temperature field:")
    
    # Create temperature field with initial conditions
    T = [20.0 + 80.0 * sin(π * xi) for xi in x]  # Sinusoidal temperature distribution
    
    println("✓ Temperature field created")
    println("  Initial conditions: T(x) = 20 + 80*sin(πx)")
    println("  Temperature range: $(@sprintf("%.1f", minimum(T))) - $(@sprintf("%.1f", maximum(T)))°C")
    
    # Demonstrate field operations
    println("\n🔧 Field operations:")
    
    # Calculate field statistics
    T_avg = sum(T) / length(T)
    T_std = sqrt(sum((T .- T_avg).^2) / length(T))
    
    println("  Average temperature: $(@sprintf("%.1f", T_avg))°C")
    println("  Standard deviation: $(@sprintf("%.1f", T_std))°C")
    
    # Calculate gradients (finite differences)
    println("\n∇ Gradient calculation:")
    dT_dx = Float64[]
    for i in 2:n_cells-1
        gradient = (T[i+1] - T[i-1]) / (x[i+1] - x[i-1])
        push!(dT_dx, gradient)
    end
    
    max_gradient = maximum(abs.(dT_dx))
    println("  Maximum |dT/dx|: $(@sprintf("%.1f", max_gradient)) °C/m")
    
    # Calculate second derivatives (Laplacian in 1D)
    println("\n∇² Laplacian calculation:")
    d2T_dx2 = Float64[]
    dx = x[2] - x[1]  # uniform spacing
    
    for i in 2:n_cells-1
        laplacian = (T[i+1] - 2*T[i] + T[i-1]) / dx^2
        push!(d2T_dx2, laplacian)
    end
    
    max_laplacian = maximum(abs.(d2T_dx2))
    println("  Maximum |d²T/dx²|: $(@sprintf("%.1f", max_laplacian)) °C/m²")
    
    println("✓ Field operations completed")
    
    field_data = (
        T = T,
        dT_dx = dT_dx,
        d2T_dx2 = d2T_dx2,
        statistics = (avg=T_avg, std=T_std, max_grad=max_gradient, max_lapl=max_laplacian)
    )
    
    return field_data
end

# ============================================================================
# Example 4: Dictionary-style Configuration
# ============================================================================

function example_4_configuration()
    println("\n📋 Example 4: Dictionary-style Configuration")
    println("─" ^ 45)
    
    # Create OpenFOAM-style configuration
    println("Creating OpenFOAM-style case configuration:")
    
    # Control dictionary (like controlDict)
    control_dict = Dict(
        "application" => "simpleFoam",
        "startTime" => 0.0,
        "endTime" => 100.0,
        "deltaT" => 0.01,
        "writeControl" => "timeStep",
        "writeInterval" => 10,
        "writeFormat" => "ascii",
        "writePrecision" => 6
    )
    
    # Numerical schemes (like fvSchemes)  
    schemes_dict = Dict(
        "ddtSchemes" => Dict("default" => "Euler"),
        "gradSchemes" => Dict("default" => "Gauss linear"),
        "divSchemes" => Dict(
            "default" => "none",
            "div(phi,U)" => "Gauss linearUpwind grad(U)",
            "div(phi,T)" => "Gauss limitedLinear 1"
        ),
        "laplacianSchemes" => Dict("default" => "Gauss linear corrected"),
        "interpolationSchemes" => Dict("default" => "linear")
    )
    
    # Solution control (like fvSolution)
    solution_dict = Dict(
        "solvers" => Dict(
            "p" => Dict(
                "solver" => "PCG",
                "preconditioner" => "DIC", 
                "tolerance" => 1e-6,
                "relTol" => 0.01
            ),
            "U" => Dict(
                "solver" => "PBiCGStab",
                "preconditioner" => "DILU",
                "tolerance" => 1e-6,
                "relTol" => 0.1
            )
        ),
        "SIMPLE" => Dict(
            "nNonOrthogonalCorrectors" => 2,
            "residualControl" => Dict(
                "p" => 1e-6,
                "U" => 1e-6
            )
        )
    )
    
    # Physical properties (like transportProperties)
    properties_dict = Dict(
        "transportModel" => "Newtonian",
        "nu" => 1e-5,     # kinematic viscosity [m²/s]
        "rho" => 1.0,     # density [kg/m³]
        "Cp" => 1005.0,   # specific heat [J/kg/K]
        "Pr" => 0.7       # Prandtl number
    )
    
    # Display configuration
    println("✓ Configuration created:")
    
    println("  Control parameters:")
    for (key, value) in control_dict
        println("    $key: $value")
    end
    
    println("  Solver settings:")
    for (solver, settings) in solution_dict["solvers"]
        println("    $solver solver: $(settings["solver"]), tol: $(settings["tolerance"])")
    end
    
    println("  Physical properties:")
    for (prop, value) in properties_dict
        println("    $prop: $value")
    end
    
    # Demonstrate accessing nested values
    println("\n🔍 Accessing configuration values:")
    end_time = control_dict["endTime"]
    p_tolerance = solution_dict["solvers"]["p"]["tolerance"] 
    viscosity = properties_dict["nu"]
    
    println("  End time: $(end_time) s")
    println("  Pressure solver tolerance: $(p_tolerance)")
    println("  Kinematic viscosity: $(viscosity) m²/s")
    
    println("✓ Dictionary-style configuration demonstrated")
    
    config = (
        control = control_dict,
        schemes = schemes_dict,
        solution = solution_dict,
        properties = properties_dict
    )
    
    return config
end

# ============================================================================
# Example 5: Basic Monitoring Concepts
# ============================================================================

function example_5_monitoring()
    println("\n📊 Example 5: Basic Monitoring Concepts")
    println("─" ^ 40)
    
    # Simulate a time loop with monitoring
    println("Simulating time loop with monitoring:")
    
    n_steps = 20
    dt = 0.1
    
    # Initialize monitoring data
    time_history = Float64[]
    residual_history = Float64[]
    force_history = Float64[]
    
    println("\n⏰ Time loop simulation:")
    println("Step |  Time  | Residual | Force  | Status")
    println("-----|--------|----------|--------|--------")
    
    for step in 1:n_steps
        time = step * dt
        
        # Simulate decreasing residuals (convergence)
        residual = 1e-2 * exp(-0.3 * step) * (1 + 0.1 * sin(step))
        
        # Simulate force oscillation 
        force = 10.0 * (1 + 0.2 * sin(2π * time / 5)) + 0.5 * randn()
        
        # Store history
        push!(time_history, time)
        push!(residual_history, residual)
        push!(force_history, force)
        
        # Determine status
        status = if residual < 1e-6
            "Conv"
        elseif residual < 1e-4  
            "Good"
        elseif residual < 1e-3
            "OK"
        else
            "High"
        end
        
        # Display progress
        if step % 4 == 0 || step <= 3 || step == n_steps
            println("$(@sprintf("%4d", step)) | $(@sprintf("%6.1f", time)) | $(@sprintf("%8.2e", residual)) | $(@sprintf("%6.1f", force)) | $status")
        end
        
        # Check convergence
        if residual < 1e-6
            println("     | $(@sprintf("%6.1f", time)) |          |        | ✓ Converged!")
            break
        end
    end
    
    # Calculate monitoring statistics
    final_residual = residual_history[end]
    avg_force = sum(force_history) / length(force_history)
    max_force = maximum(force_history)
    min_force = minimum(force_history)
    
    println("\n📈 Monitoring summary:")
    println("  Final residual: $(@sprintf("%.2e", final_residual))")
    println("  Force statistics:")
    println("    Average: $(@sprintf("%.1f", avg_force)) N")
    println("    Range: $(@sprintf("%.1f", min_force)) - $(@sprintf("%.1f", max_force)) N")
    println("    Variation: ±$(@sprintf("%.1f", (max_force - min_force)/2)) N")
    
    println("✓ Basic monitoring demonstrated")
    
    monitoring_data = (
        time = time_history,
        residuals = residual_history,
        forces = force_history,
        statistics = (
            final_residual = final_residual,
            avg_force = avg_force,
            force_range = (min_force, max_force)
        )
    )
    
    return monitoring_data
end

# ============================================================================
# Example 6: Simple Optimization Concepts
# ============================================================================

function example_6_optimization()
    println("\n⚡ Example 6: Simple Optimization Concepts")
    println("─" ^ 45)
    
    # Demonstrate different solution methods
    println("Matrix solution method comparison:")
    
    # Create a simple test system: Ax = b
    n = 5
    A = [
        4.0 -1.0  0.0  0.0  0.0;
       -1.0  4.0 -1.0  0.0  0.0;
        0.0 -1.0  4.0 -1.0  0.0;
        0.0  0.0 -1.0  4.0 -1.0;
        0.0  0.0  0.0 -1.0  4.0
    ]
    b = [1.0, 2.0, 3.0, 2.0, 1.0]
    
    println("✓ Test system created: 5×5 tridiagonal matrix")
    
    # Method 1: Direct solve (backslash operator)
    println("\n🔧 Method comparison:")
    
    t1 = @elapsed x1 = A \ b
    println("  Direct solve (\\): $(@sprintf("%.6f", t1)) seconds")
    
    # Method 2: LU decomposition
    t2 = @elapsed begin
        LU = lu(A)
        x2 = LU \ b
    end
    println("  LU decomposition: $(@sprintf("%.6f", t2)) seconds")
    
    # Method 3: Iterative (simple Jacobi)
    t3 = @elapsed begin
        x3 = zeros(n)
        for iter in 1:50
            x_old = copy(x3)
            for i in 1:n
                sum_ax = sum(A[i,j] * x_old[j] for j in 1:n if j != i)
                x3[i] = (b[i] - sum_ax) / A[i,i]
            end
            if norm(x3 - x_old) < 1e-10
                break
            end
        end
    end
    println("  Jacobi iteration: $(@sprintf("%.6f", t3)) seconds")
    
    # Check solutions
    error1 = norm(A * x1 - b)
    error2 = norm(A * x2 - b)
    error3 = norm(A * x3 - b)
    
    println("\n📊 Solution accuracy:")
    println("  Direct solve error: $(@sprintf("%.2e", error1))")
    println("  LU decomposition error: $(@sprintf("%.2e", error2))")
    println("  Jacobi iteration error: $(@sprintf("%.2e", error3))")
    
    # Optimization concepts
    println("\n💡 Optimization concepts:")
    println("  • Matrix structure: Tridiagonal (sparse)")
    println("  • Condition number: $(@sprintf("%.1f", cond(A)))")
    println("  • Memory usage: $(sizeof(A)) bytes (dense), ~$(3*n*8) bytes (sparse)")
    println("  • Best method: Direct solve (for small systems)")
    println("  • For large systems: Sparse iterative methods preferred")
    
    println("✓ Optimization concepts demonstrated")
    
    optimization_data = (
        solutions = (direct=x1, lu=x2, iterative=x3),
        timings = (direct=t1, lu=t2, iterative=t3),
        errors = (direct=error1, lu=error2, iterative=error3),
        matrix_properties = (size=size(A), condition=cond(A))
    )
    
    return optimization_data
end

# ============================================================================
# Main Function - Run All Examples
# ============================================================================

function run_all_working_examples()
    println("\n🎯 Running All Working Quick Start Examples...")
    
    results = Dict()
    
    try
        results["math"] = example_1_basic_math()
        results["mesh"] = example_2_mesh_concepts()
        results["fields"] = example_3_field_operations()
        results["config"] = example_4_configuration()
        results["monitoring"] = example_5_monitoring()
        results["optimization"] = example_6_optimization()
        
        println("\n" ^ 2)
        println("🎉 All Working Examples Completed Successfully!")
        println("=" ^ 55)
        
        println("Summary of Working Examples:")
        println("1. ✓ Basic CFD mathematical operations")
        println("2. ✓ Simple mesh concepts and operations") 
        println("3. ✓ Field creation and mathematical operations")
        println("4. ✓ Dictionary-style configuration (OpenFOAM-like)")
        println("5. ✓ Basic monitoring and convergence tracking")
        println("6. ✓ Simple optimization method comparison")
        
        println("\nKey Concepts Demonstrated:")
        println("• Vector and scalar field operations")
        println("• Finite difference discretization")
        println("• Mesh generation and quality metrics")
        println("• OpenFOAM-style case configuration")
        println("• Real-time monitoring and diagnostics")
        println("• Matrix solution method comparison")
        println("• Convergence detection and analysis")
        
        println("\n🚀 CFD.jl Enhanced Ecosystem - Ready for Advanced Examples!")
        
        return (results=results, success=true)
        
    catch e
        println("❌ Some examples failed: $(typeof(e).name)")
        println("Error details: $e")
        return (results=results, success=false, error=e)
    end
end

# Run examples if script is executed directly
if abspath(PROGRAM_FILE) == @__FILE__
    result = run_all_working_examples()
    println("\n✅ Working example results available in 'result' variable")
end