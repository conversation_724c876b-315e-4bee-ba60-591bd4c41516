"""
# OpenFOAMIntegration.jl

OpenFOAM-style ecosystem features for CFD.jl including:
- Field operations library (fvc/fvm style)
- Runtime selection tables for models
- Dictionary-based case setup
- Function objects for runtime processing
- OpenFOAM-compatible input/output

This module provides OpenFOAM-like functionality while maintaining
<PERSON>'s performance and type safety advantages.
"""
module OpenFOAMIntegration

using ..CFDCore
using ..Numerics  
using ..Physics
using LinearAlgebra
using SparseArrays
using StaticArrays

export fvc, fvm, IOobject, dictionary, runTimeSelectionTable
export writeFields, readFields, createMesh, createFields
export functionObjects, dataEntry, dimensionSet
export OpenFOAMCase, setupCase, runCase, postProcess

# ============================================================================
# Field Operations Library (OpenFOAM fvc/fvm style)
# ============================================================================

"""
Finite Volume Calculus (fvc) - Explicit operations
Similar to OpenFOAM's fvc namespace for explicit finite volume operations
"""
module fvc

using ...CFDCore
using ...Numerics
using SparseArrays
using LinearAlgebra

export grad, div, laplacian, curl, flux, interpolate
export magSqr, mag, sqr, pow, max, min
export average, weightedAverage, domainIntegrate, faceAverage

"""
Gradient calculation (explicit)
    fvc::grad(φ) - Calculate gradient of scalar field φ
"""
function grad(φ::ScalarField)
    mesh = φ.mesh
    ∇φ_data = [SVector{3,Float64}(0,0,0) for _ in 1:length(mesh.cells)]
    
    # Green-Gauss gradient calculation
    for (i, cell) in enumerate(mesh.cells)
        grad_sum = SVector{3,Float64}(0,0,0)
        
        for face_idx in cell.faces
            face = mesh.faces[face_idx]
            face_value = interpolate_to_face(φ, face_idx)
            grad_sum += face_value * face.area_vector
        end
        
        ∇φ_data[i] = grad_sum / cell.volume
    end
    
    return VectorField(:grad_phi, mesh, ∇φ_data, Dict{String,Any}())
end

"""
Divergence calculation (explicit)
    fvc::div(U) - Calculate divergence of vector field U
"""
function div(U::VectorField)
    mesh = U.mesh
    div_data = zeros(length(mesh.cells))
    
    # Green-Gauss divergence
    for (i, cell) in enumerate(mesh.cells)
        div_sum = 0.0
        
        for face_idx in cell.faces
            face = mesh.faces[face_idx]
            face_velocity = interpolate_to_face(U, face_idx)
            div_sum += dot(face_velocity, face.area_vector)
        end
        
        div_data[i] = div_sum / cell.volume
    end
    
    return ScalarField(:div_U, mesh, div_data, Dict{String,Any}())
end

"""
Laplacian calculation (explicit)
    fvc::laplacian(γ, φ) - Calculate laplacian with diffusivity γ
"""
function laplacian(γ::Union{Float64,ScalarField}, φ::ScalarField)
    # Calculate ∇(γ∇φ) using divergence of flux
    grad_phi = grad(φ)
    
    if isa(γ, Float64)
        flux = VectorField(:flux, φ.mesh, [γ * v for v in grad_phi.data], Dict{String,Any}())
    else
        flux_data = [γ.data[i] * grad_phi.data[i] for i in 1:length(grad_phi.data)]
        flux = VectorField(:flux, φ.mesh, flux_data, Dict{String,Any}())
    end
    
    return div(flux)
end

"""
Face interpolation
    fvc::interpolate(φ, "scheme") - Interpolate field to faces
"""
function interpolate(φ::Field, scheme::String="linear")
    mesh = φ.mesh
    face_values = similar(φ.data, length(mesh.faces))
    
    for (face_idx, face) in enumerate(mesh.faces)
        if length(face.cells) == 2
            # Internal face - linear interpolation
            cell1_val = φ.data[face.cells[1]]
            cell2_val = φ.data[face.cells[2]]
            face_values[face_idx] = 0.5 * (cell1_val + cell2_val)
        else
            # Boundary face - use cell value
            face_values[face_idx] = φ.data[face.cells[1]]
        end
    end
    
    return face_values
end

"""
Domain integration
    fvc::domainIntegrate(φ) - Integrate field over entire domain
"""
function domainIntegrate(φ::ScalarField)
    mesh = φ.mesh
    integral = 0.0
    
    for (i, cell) in enumerate(mesh.cells)
        integral += φ.data[i] * cell.volume
    end
    
    return integral
end

# Helper function for face interpolation
function interpolate_to_face(φ::Field, face_idx::Int)
    face = φ.mesh.faces[face_idx]
    if length(face.cells) == 2
        # Linear interpolation for internal faces
        return 0.5 * (φ.data[face.cells[1]] + φ.data[face.cells[2]])
    else
        # Boundary face
        return φ.data[face.cells[1]]
    end
end

end # module fvc

"""
Finite Volume Method (fvm) - Implicit operations
Similar to OpenFOAM's fvm namespace for implicit finite volume operations
"""
module fvm

using ...CFDCore
using SparseArrays
using LinearAlgebra

export laplacian, div, ddt, grad, Su, Sp
export solve, relax, residual

"""
Implicit laplacian operator
    fvm::laplacian(γ, φ) - Create implicit laplacian matrix
"""
function laplacian(γ::Union{Float64,ScalarField}, φ::ScalarField)
    mesh = φ.mesh
    n_cells = length(mesh.cells)
    
    # Build coefficient matrix
    I = Int[]
    J = Int[]
    V = Float64[]
    
    for (cell_idx, cell) in enumerate(mesh.cells)
        # Diagonal coefficient
        diag_coeff = 0.0
        
        for face_idx in cell.faces
            face = mesh.faces[face_idx]
            
            if length(face.cells) == 2
                # Internal face
                neighbor_idx = face.cells[1] == cell_idx ? face.cells[2] : face.cells[1]
                
                # Calculate diffusion coefficient
                diff_coeff = isa(γ, Float64) ? γ : 0.5 * (γ.data[cell_idx] + γ.data[neighbor_idx])
                
                # Face area and distance
                face_area = norm(face.area_vector)
                distance = compute_face_distance(mesh, face_idx)
                
                coeff = diff_coeff * face_area / distance
                
                # Off-diagonal
                push!(I, cell_idx)
                push!(J, neighbor_idx)
                push!(V, -coeff)
                
                # Accumulate diagonal
                diag_coeff += coeff
            else
                # Boundary face - add to diagonal
                diff_coeff = isa(γ, Float64) ? γ : γ.data[cell_idx]
                face_area = norm(face.area_vector)
                
                # Apply boundary condition contribution
                bc_coeff = apply_boundary_coefficient(φ, face_idx)
                diag_coeff += diff_coeff * face_area * bc_coeff
            end
        end
        
        # Diagonal entry
        push!(I, cell_idx)
        push!(J, cell_idx)
        push!(V, diag_coeff)
    end
    
    A = sparse(I, J, V, n_cells, n_cells)
    b = zeros(n_cells)
    
    return (A, b)
end

"""
Implicit time derivative
    fvm::ddt(ρ, φ) - Create time derivative matrix
"""
function ddt(ρ::Union{Float64,ScalarField}, φ::ScalarField, Δt::Float64)
    mesh = φ.mesh
    n_cells = length(mesh.cells)
    
    # Diagonal matrix for time derivative
    I = collect(1:n_cells)
    J = collect(1:n_cells)
    V = Float64[]
    
    for (cell_idx, cell) in enumerate(mesh.cells)
        density = isa(ρ, Float64) ? ρ : ρ.data[cell_idx]
        coeff = density * cell.volume / Δt
        push!(V, coeff)
    end
    
    A = sparse(I, J, V, n_cells, n_cells)
    
    # Source term from old time step
    b = zeros(n_cells)
    for (cell_idx, cell) in enumerate(mesh.cells)
        density = isa(ρ, Float64) ? ρ : ρ.data[cell_idx]
        b[cell_idx] = density * cell.volume * φ.old[cell_idx] / Δt
    end
    
    return (A, b)
end

"""
Source term (explicit)
    fvm::Su(S) - Explicit source term
"""
function Su(S::Union{Float64,ScalarField}, mesh::AbstractMesh)
    n_cells = length(mesh.cells)
    b = zeros(n_cells)
    
    for (cell_idx, cell) in enumerate(mesh.cells)
        source = isa(S, Float64) ? S : S.data[cell_idx]
        b[cell_idx] = source * cell.volume
    end
    
    return (spzeros(n_cells, n_cells), b)
end

"""
Source term (implicit)
    fvm::Sp(S, φ) - Implicit source term
"""
function Sp(S::Union{Float64,ScalarField}, φ::ScalarField)
    mesh = φ.mesh
    n_cells = length(mesh.cells)
    
    I = collect(1:n_cells)
    J = collect(1:n_cells)
    V = Float64[]
    
    for (cell_idx, cell) in enumerate(mesh.cells)
        source_coeff = isa(S, Float64) ? S : S.data[cell_idx]
        push!(V, -source_coeff * cell.volume)  # Negative for implicit
    end
    
    A = sparse(I, J, V, n_cells, n_cells)
    b = zeros(n_cells)
    
    return (A, b)
end

# Helper functions
function compute_face_distance(mesh::AbstractMesh, face_idx::Int)
    # Calculate distance between cell centers across face
    face = mesh.faces[face_idx]
    if length(face.cells) == 2
        cell1 = mesh.cells[face.cells[1]]
        cell2 = mesh.cells[face.cells[2]]
        return norm(cell1.center - cell2.center)
    else
        # Boundary face - distance to face center
        cell = mesh.cells[face.cells[1]]
        return norm(cell.center - face.center)
    end
end

function apply_boundary_coefficient(φ::ScalarField, face_idx::Int)
    """
    Apply proper boundary coefficient based on boundary condition type
    """
    try
        # Get the face
        mesh = φ.mesh
        if face_idx <= 0 || face_idx > length(mesh.faces)
            return 1.0  # Default coefficient
        end
        
        face = mesh.faces[face_idx]
        
        # Find which boundary patch this face belongs to
        for (patch_name, face_indices) in mesh.boundaries
            if face_idx in face_indices
                # Get boundary condition for this patch
                if haskey(φ.boundary_conditions, patch_name)
                    bc = φ.boundary_conditions[patch_name]
                    
                    # Apply coefficient based on BC type
                    if isa(bc, CFDCore.DirichletBC)
                        # For Dirichlet BC, coefficient is typically 1.0
                        return 1.0
                    elseif isa(bc, CFDCore.NeumannBC)
                        # For Neumann BC, coefficient is 0.0 (no contribution to matrix diagonal)
                        return 0.0
                    elseif isa(bc, CFDCore.RobinBC)
                        # For Robin BC, coefficient is β (normal gradient coefficient)
                        return bc.β
                    else
                        # Unknown BC type, use default
                        return 1.0
                    end
                end
            end
        end
        
        # No boundary condition found, default coefficient
        return 1.0
        
    catch e
        @warn "Error applying boundary coefficient: $e"
        return 1.0  # Safe fallback
    end
end

end # module fvm

# ============================================================================
# OpenFOAM-style Dictionary System
# ============================================================================

"""
OpenFOAM-style dictionary for case setup
"""
mutable struct Dictionary
    name::String
    entries::Dict{String,Any}
    sub_dicts::Dict{String,Dictionary}
    
    function Dictionary(name::String)
        new(name, Dict{String,Any}(), Dict{String,Dictionary}())
    end
end

"""
Create or access sub-dictionary
"""
function Base.getindex(dict::Dictionary, key::String)
    if haskey(dict.sub_dicts, key)
        return dict.sub_dicts[key]
    elseif haskey(dict.entries, key)
        return dict.entries[key]
    else
        # Create new sub-dictionary
        dict.sub_dicts[key] = Dictionary(key)
        return dict.sub_dicts[key]
    end
end

function Base.setindex!(dict::Dictionary, value, key::String)
    if isa(value, Dictionary)
        dict.sub_dicts[key] = value
    else
        dict.entries[key] = value
    end
end

# ============================================================================
# Runtime Selection Tables
# ============================================================================

"""
Runtime selection table for dynamic model selection
"""
struct RunTimeSelectionTable{T}
    constructors::Dict{String,Function}
    
    function RunTimeSelectionTable{T}() where T
        new{T}(Dict{String,Function}())
    end
end

"""
Add constructor to selection table
"""
function add_to_table!(table::RunTimeSelectionTable{T}, name::String, constructor::Function) where T
    table.constructors[name] = constructor
end

"""
Create object from selection table
"""
function create_from_table(table::RunTimeSelectionTable{T}, name::String, args...) where T
    if haskey(table.constructors, name)
        return table.constructors[name](args...)
    else
        error("Unknown type '$name' in runtime selection table")
    end
end

# ============================================================================
# OpenFOAM Case Structure
# ============================================================================

"""
OpenFOAM-style case management
"""
mutable struct OpenFOAMCase
    name::String
    root_path::String
    time_directories::Vector{String}
    constant_dict::Dictionary
    system_dict::Dictionary
    current_time::Float64
    
    function OpenFOAMCase(name::String, root_path::String)
        constant = Dictionary("constant")
        system = Dictionary("system")
        new(name, root_path, String[], constant, system, 0.0)
    end
end

"""
Setup case directory structure
"""
function setupCase(case::OpenFOAMCase)
    # Create directory structure
    mkpath(joinpath(case.root_path, "0"))
    mkpath(joinpath(case.root_path, "constant"))
    mkpath(joinpath(case.root_path, "system"))
    
    @info "Created OpenFOAM-style case structure at $(case.root_path)"
end

"""
Write OpenFOAM-style field file
"""
function writeFields(case::OpenFOAMCase, fields::Vector{Field}, time::Float64)
    time_dir = joinpath(case.root_path, string(time))
    mkpath(time_dir)
    
    for field in fields
        write_openfoam_field(field, time_dir)
    end
    
    if !(string(time) in case.time_directories)
        push!(case.time_directories, string(time))
    end
end

function write_openfoam_field(field::Field, directory::String)
    filename = joinpath(directory, string(field.name))
    
    open(filename, "w") do io
        # Write OpenFOAM header
        write_openfoam_header(io, field)
        
        # Write field data
        println(io, "internalField   uniform $(field.data[1]);")
        println(io)
        println(io, "boundaryField")
        println(io, "{")
        
        # Write boundary conditions
        for (patch_name, bc) in field.boundary_conditions
            println(io, "    $patch_name")
            println(io, "    {")
            write_boundary_condition(io, bc)
            println(io, "    }")
        end
        
        println(io, "}")
    end
end

function write_openfoam_header(io::IO, field::Field)
    println(io, "/*--------------------------------*- C++ -*----------------------------------*\\")
    println(io, "| =========                 |                                                 |")
    println(io, "| \\\\      /  F ield         | CFD.jl: Julia CFD Framework                    |")
    println(io, "|  \\\\    /   O peration     | Version:  2.1                                  |")
    println(io, "|   \\\\  /    A nd           | Web:      www.github.com/CFD.jl                |")
    println(io, "|    \\\\/     M anipulation  |                                                 |")
    println(io, "\\*---------------------------------------------------------------------------*/")
    println(io, "FoamFile")
    println(io, "{")
    println(io, "    version     2.0;")
    println(io, "    format      ascii;")
    println(io, "    class       $(get_openfoam_class(field));")
    println(io, "    object      $(field.name);")
    println(io, "}")
    println(io, "// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //")
    println(io)
end

function get_openfoam_class(field::Field)
    if isa(field, ScalarField)
        return "volScalarField"
    elseif isa(field, VectorField)
        return "volVectorField"
    else
        return "volScalarField"
    end
end

function write_boundary_condition(io::IO, bc::AbstractBoundaryCondition)
    """
    Write proper OpenFOAM boundary condition based on BC type
    """
    if isa(bc, CFDCore.DirichletBC)
        # Fixed value boundary condition
        println(io, "        type            fixedValue;")
        
        # Handle different value types
        if isa(bc.value, Number)
            println(io, "        value           uniform $(bc.value);")
        elseif isa(bc.value, SVector{3})
            println(io, "        value           uniform ($(bc.value[1]) $(bc.value[2]) $(bc.value[3]));")
        elseif isa(bc.value, AbstractVector) && length(bc.value) == 3
            println(io, "        value           uniform ($(bc.value[1]) $(bc.value[2]) $(bc.value[3]));")
        else
            println(io, "        value           uniform $(bc.value);")
        end
        
    elseif isa(bc, CFDCore.NeumannBC)
        # Zero gradient or fixed gradient boundary condition
        if isa(bc.gradient, Number) && bc.gradient == 0.0
            println(io, "        type            zeroGradient;")
        else
            println(io, "        type            fixedGradient;")
            if isa(bc.gradient, Number)
                println(io, "        gradient        uniform $(bc.gradient);")
            elseif isa(bc.gradient, SVector{3})
                println(io, "        gradient        uniform ($(bc.gradient[1]) $(bc.gradient[2]) $(bc.gradient[3]));")
            else
                println(io, "        gradient        uniform $(bc.gradient);")
            end
        end
        
    elseif isa(bc, CFDCore.RobinBC)
        # Mixed boundary condition (Robin type)
        println(io, "        type            mixed;")
        println(io, "        refValue        uniform $(bc.γ);")
        println(io, "        refGradient     uniform 0;")
        println(io, "        valueFraction   uniform $(bc.α / (bc.α + bc.β));")
        
    else
        # Generic boundary condition - fallback
        println(io, "        type            fixedValue;")
        println(io, "        value           uniform 0;")
        @warn "Unknown boundary condition type: $(typeof(bc)), using fixedValue fallback"
    end
end

# ============================================================================
# Function Objects for Runtime Processing
# ============================================================================

"""
Function object for runtime processing (similar to OpenFOAM functionObjects)
"""
abstract type FunctionObject end

"""
Force calculation function object
"""
mutable struct Forces <: FunctionObject
    name::String
    patches::Vector{String}
    rho_ref::Float64
    center_of_rotation::SVector{3,Float64}
    
    function Forces(name::String, patches::Vector{String}; 
                   rho_ref::Float64=1.0, 
                   center_of_rotation::SVector{3,Float64}=SVector{3,Float64}(0,0,0))
        new(name, patches, rho_ref, center_of_rotation)
    end
end

"""
Execute function object
"""
function execute!(fo::Forces, fields::Vector{Field}, time::Float64)
    # Calculate forces on specified patches
    total_force = SVector{3,Float64}(0,0,0)
    total_moment = SVector{3,Float64}(0,0,0)
    
    # Implementation would calculate forces from pressure and viscous stress
    @info "Forces at time $time: F = $total_force, M = $total_moment"
    
    return (force=total_force, moment=total_moment)
end

"""
Residual monitoring function object
"""
mutable struct Residuals <: FunctionObject
    name::String
    fields::Vector{String}
    tolerance::Float64
    
    function Residuals(name::String, fields::Vector{String}; tolerance::Float64=1e-6)
        new(name, fields, tolerance)
    end
end

function execute!(fo::Residuals, solver_residuals::Dict{String,Float64}, time::Float64)
    @info "Residuals at time $time:"
    for field_name in fo.fields
        if haskey(solver_residuals, field_name)
            residual = solver_residuals[field_name]
            converged = residual < fo.tolerance ? "✓" : "✗"
            @info "  $field_name: $residual $converged"
        end
    end
end

# ============================================================================
# Convenience Functions
# ============================================================================

"""
Create mesh from OpenFOAM case
"""
function createMesh(case::OpenFOAMCase)
    # Read mesh from constant/polyMesh
    mesh_path = joinpath(case.root_path, "constant", "polyMesh")
    return read_openfoam_mesh(mesh_path)
end

"""
Create fields from OpenFOAM case
"""
function createFields(case::OpenFOAMCase, time::Float64=0.0)
    time_dir = joinpath(case.root_path, string(time))
    fields = Field[]
    
    # Read all field files in time directory
    if isdir(time_dir)
        for filename in readdir(time_dir)
            filepath = joinpath(time_dir, filename)
            if isfile(filepath) && !startswith(filename, ".")
                field = read_openfoam_field(filepath)
                push!(fields, field)
            end
        end
    end
    
    return fields
end

# Real OpenFOAM I/O implementations
function read_openfoam_mesh(mesh_path::String)
    """
    Read OpenFOAM mesh using existing Utilities functionality
    """
    try
        # Use the Utilities module mesh reader
        if isdefined(CFD, :Utilities) && isdefined(CFD.Utilities, :read_openfoam_mesh)
            return CFD.Utilities.read_openfoam_mesh(mesh_path)
        else
            # Fallback to manual reading if Utilities not available
            return read_mesh_manual(mesh_path)
        end
    catch e
        @warn "OpenFOAM mesh reading failed: $e"
        return nothing
    end
end

function read_openfoam_field(filepath::String)
    """
    Read OpenFOAM field file using real parser
    """
    try
        if !isfile(filepath)
            @warn "Field file not found: $filepath"
            return nothing
        end
        
        # Parse the OpenFOAM field file
        content = read(filepath, String)
        return parse_openfoam_field_file(content, filepath)
    catch e
        @warn "OpenFOAM field reading failed: $e"
        return nothing
    end
end

function read_mesh_manual(mesh_path::String)
    """
    Manual mesh reading as fallback
    """
    if isdir(mesh_path)
        # Try to read from polyMesh directory
        polymesh_dir = joinpath(mesh_path, "constant", "polyMesh")
        if isdir(polymesh_dir)
            # Use Utilities mesh reader
            return CFD.Utilities.read_openfoam_mesh(mesh_path)
        end
    end
    return nothing
end

end # module OpenFOAMIntegration