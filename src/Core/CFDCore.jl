# src/Core/CFDCore.jl
module CFDCore

export AbstractMesh, AbstractField, AbstractBoundaryCondition, extract_scalar_component
export StructuredMesh, UnstructuredMesh, Field, ScalarField, VectorField, TensorField
export Cell, Face, Node, BoundaryCondition, DirichletBC, NeumannBC, RobinBC

# Mathematical Unicode symbols for enhanced readability
# Type \phi<Tab> for φ, \rho<Tab> for ρ, \nu<Tab> for ν, etc.
export φ, ρ, 𝐮, ν, μ, γ, α, β, κ, τ, σ, ω, ε, Δt, ∇, Δ, ∂  # Common CFD symbols
export scalarField, vectorField, tensorField  # Unicode-friendly aliases
export volScalarField, volVectorField, volTensorField  # OpenFOAM-style aliases
export surfaceScalarField, surfaceVectorField  # Surface field aliases

# Export OpenFOAM field name mappings
export openfoam_to_unicode, unicode_to_openfoam
export UNICODE_FIELD_MAP, OPENFOAM_FIELD_MAP

# Export dimensional analysis
export Dimensions, dimless, dimTime, dimLength, dimArea, dimVolume
export dimVelocity, dimAcceleration, dimPressure, dimDensity
export dimKinematicViscosity, dimDynamicViscosity, dimEnergy, dimTemperature, dimSpecificHeat

using LinearAlgebra
using SparseArrays
using StaticArrays

# Optional dependencies - make them conditional
const HAS_MPI = try
    using MPI
    true
catch
    false
end

const HAS_CUDA = try
    using CUDA
    true
catch
    false
end

# ============================================================================
# Dimensional Analysis System
# ============================================================================

# Dimensions type for dimensional analysis
struct Dimensions
    M::Int     # Mass
    L::Int     # Length
    T::Int     # Time
    K::Int     # Temperature
    mol::Int   # Amount of substance
    A::Int     # Electric current
    cd::Int    # Luminous intensity
    
    function Dimensions(M=0, L=0, T=0, K=0, mol=0, A=0, cd=0)
        new(M, L, T, K, mol, A, cd)
    end
end

# Common dimension sets
const dimless = Dimensions()
const dimTime = Dimensions(0, 0, 1)
const dimLength = Dimensions(0, 1, 0)
const dimArea = Dimensions(0, 2, 0)
const dimVolume = Dimensions(0, 3, 0)
const dimVelocity = Dimensions(0, 1, -1)
const dimAcceleration = Dimensions(0, 1, -2)
const dimPressure = Dimensions(1, -1, -2)
const dimDensity = Dimensions(1, -3, 0)
const dimKinematicViscosity = Dimensions(0, 2, -1)
const dimDynamicViscosity = Dimensions(1, -1, -1)
const dimEnergy = Dimensions(1, 2, -2, 0, 0, 0, 0)  # kg⋅m²⋅s⁻²
const dimTemperature = Dimensions(0, 0, 0, 1, 0, 0, 0)  # K
const dimSpecificHeat = Dimensions(0, 2, -2, -1, 0, 0, 0)  # m²⋅s⁻²⋅K⁻¹

# Abstract types for extensibility
abstract type AbstractMesh{T,N} end
abstract type AbstractField{T,N} end
abstract type AbstractBoundaryCondition end
abstract type AbstractCell{T,N} end
abstract type AbstractFace{T,N} end

# Mesh components
struct Node{T,N}
    id::Int
    coords::SVector{N,T}
    boundary::Bool
end

struct Face{T,N}
    id::Int
    nodes::Vector{Int}
    center::SVector{N,T}
    area::T
    normal::SVector{N,T}
    owner::Int
    neighbor::Int  # -1 for boundary faces
    boundary::Bool
end

struct Cell{T,N}
    id::Int
    nodes::Vector{Int}
    faces::Vector{Int}
    center::SVector{N,T}
    volume::T
end

# Mesh types
struct UnstructuredMesh{T,N} <: AbstractMesh{T,N}
    nodes::Vector{Node{T,N}}
    faces::Vector{Face{T,N}}
    cells::Vector{Cell{T,N}}
    boundaries::Dict{String, Vector{Int}}  # boundary name => face indices
    
    # Connectivity information
    cell_to_cell::Vector{Vector{Int}}
    face_to_cell::Vector{Tuple{Int,Int}}
    
    # Geometric properties
    bbox::Tuple{SVector{N,T}, SVector{N,T}}  # bounding box
end

struct StructuredMesh{T,N} <: AbstractMesh{T,N}
    dims::NTuple{N,Int}
    origin::SVector{N,T}
    spacing::SVector{N,T}
    nodes::Array{Node{T,N},N}
    cells::Array{Cell{T,N},N}
end

# Field types with lazy evaluation support
struct Field{T,N,M<:AbstractMesh} <: AbstractField{T,N}
    name::Symbol
    mesh::M
    data::AbstractArray{T}
    boundary_conditions::Dict{String, AbstractBoundaryCondition}
    
    # Time levels for temporal schemes
    old::Union{Nothing, AbstractArray{T}}
    old_old::Union{Nothing, AbstractArray{T}}
    
    # Constructor with optional time levels
    function Field{T,N,M}(name::Symbol, mesh::M, data::AbstractArray{T}, boundary_conditions::Dict{String, AbstractBoundaryCondition}, 
                         old::Union{Nothing, AbstractArray{T}}=nothing, old_old::Union{Nothing, AbstractArray{T}}=nothing) where {T,N,M<:AbstractMesh}
        new{T,N,M}(name, mesh, data, boundary_conditions, old, old_old)
    end
end

const ScalarField{T,N,M} = Field{T,N,M}
const VectorField{T,N,M} = Field{SVector{N,T},N,M}
const TensorField{T,N,M} = Field{SMatrix{N,N,T},N,M}

# Convenience constructors for ScalarField and VectorField
function ScalarField(name::Symbol, mesh::M, data::AbstractArray{T}, boundary_conditions::Dict{String, AbstractBoundaryCondition}, 
                    old::Union{Nothing, AbstractArray{T}}=nothing, old_old::Union{Nothing, AbstractArray{T}}=nothing) where {T,N,M<:AbstractMesh{T,N}}
    Field{T,N,M}(name, mesh, data, boundary_conditions, old, old_old)
end

function VectorField(name::Symbol, mesh::M, data::AbstractArray{SVector{N,T}}, boundary_conditions::Dict{String, AbstractBoundaryCondition}, 
                    old::Union{Nothing, AbstractArray{SVector{N,T}}}=nothing, old_old::Union{Nothing, AbstractArray{SVector{N,T}}}=nothing) where {T,N,M<:AbstractMesh{T,N}}
    return Field{SVector{N,T},N,M}(name, mesh, data, boundary_conditions, old, old_old)
end

# Helper function to extract a scalar component field from a vector field
"""
    extract_scalar_component(vf::VectorField, component_idx::Int; component_name_suffix::String="")

Extracts a scalar component field from a given VectorField.

`vf`: The source VectorField.
`component_idx`: The index of the component to extract (e.g., 1 for x, 2 for y).
`component_name_suffix`: Optional suffix to append to the new scalar field's name.

Returns a `ScalarField` representing the specified component.
Boundary conditions are transformed: DirichletBCs on the VectorField become DirichletBCs
on the ScalarField by extracting the corresponding component of the vector value.
NeumannBCs are assumed to have a scalar gradient applicable to the component.
"""
function extract_scalar_component(vf::VectorField{TF, N, M}, component_idx::Int; component_name_suffix::String="") where {N, TF, M<:AbstractMesh}
    if !(1 <= component_idx <= N)
        error("Invalid component_idx $component_idx for VectorField with $N components.")
    end

    mesh = vf.mesh
    scalar_data = [v[component_idx] for v in vf.data] # Corrected s_data to scalar_data
    
    new_name_str = string(vf.name) * (isempty(component_name_suffix) ? "_$(component_idx)" : component_name_suffix)
    new_name = Symbol(new_name_str)

    # Transform boundary conditions
    scalar_bcs = Dict{String, AbstractBoundaryCondition}()
    for (patch_name, vector_bc) in vf.boundary_conditions
        if vector_bc isa DirichletBC{<:SVector}
            scalar_bcs[patch_name] = DirichletBC(vector_bc.value[component_idx])
        elseif vector_bc isa DirichletBC # For scalar Dirichlet (e.g. p field treated as VectorField with 1 component)
            scalar_bcs[patch_name] = DirichletBC(vector_bc.value) # Assume it's already scalar
        elseif vector_bc isa NeumannBC
            # Assuming Neumann BC (zeroGradient or specified gradient) applies to scalar component
            scalar_bcs[patch_name] = NeumannBC(vector_bc.gradient) 
        elseif vector_bc isa RobinBC
            # Assuming Robin BC coefficients apply to scalar component
            # This might need careful consideration if α, β, γ are functions of the vector value
            scalar_bcs[patch_name] = RobinBC(vector_bc.α, vector_bc.β, vector_bc.γ) # Potentially problematic if γ is vector func
        else
            @warn "Cannot derive scalar BC from vector BC type $(typeof(vector_bc)) for patch $patch_name for field $(vf.name), component $component_idx. Omitting BC for this patch."
        end
    end

    return ScalarField{TF,N,M}(new_name, mesh, scalar_data, scalar_bcs, vf.old === nothing ? nothing : getindex.(vf.old, component_idx), vf.old_old === nothing ? nothing : getindex.(vf.old_old, component_idx))
end

# Boundary condition types
struct BoundaryCondition{T} <: AbstractBoundaryCondition
    type::Symbol  # :dirichlet, :neumann, :robin, etc.
    value::Union{T, Function, Nothing} # Value for Dirichlet, gradient for Neumann, etc. Can be a function for time/space dependent BCs
    coeffs::Union{Tuple, Nothing} # For Robin BCs (alpha, beta, gamma)
end

# Specific BC structs for type safety and clarity
struct DirichletBC{T} <: AbstractBoundaryCondition
    value::T # Can be scalar, SVector, SMatrix, or a function returning these
end

struct NeumannBC{T} <: AbstractBoundaryCondition 
    gradient::T # Typically zero for zeroGradient, or a specified scalar/vector gradient
end

struct RobinBC{T_alpha, T_beta, T_gamma} <: AbstractBoundaryCondition
    α::T_alpha # Coefficient for the field value
    β::T_beta  # Coefficient for the normal gradient
    γ::T_gamma # Constant term or function
end

# Field operations with broadcasting support
Base.:(+)(f1::Field, f2::Field) = Field(Symbol(f1.name, :_plus_, f2.name), f1.mesh, 
                                        f1.data .+ f2.data, f1.boundary_conditions)
Base.:(-)(f1::Field, f2::Field) = Field(Symbol(f1.name, :_minus_, f2.name), f1.mesh,
                                        f1.data .- f2.data, f1.boundary_conditions)
Base.:(*)(α::Number, f::Field) = Field(Symbol(α, :_times_, f.name), f.mesh,
                                       α .* f.data, f.boundary_conditions)

# Lazy field operations for memory efficiency
struct LazyFieldOp{Op,Args}
    op::Op
    args::Args
end

# GPU-aware field allocation
function allocate_field(::Type{T}, mesh::AbstractMesh, location::Symbol=:cpu) where T
    n = location == :cell ? length(mesh.cells) : length(mesh.nodes)
    if location == :gpu && HAS_CUDA
        try
            return CuArray{T}(undef, n)
        catch
            @warn "CUDA allocation failed, falling back to CPU"
            return Array{T}(undef, n)
        end
    else
        return Array{T}(undef, n)
    end
end

# ============================================================================
# Mathematical Unicode Symbol Aliases for Enhanced Scientific Readability
# ============================================================================

# These provide intuitive mathematical notation that mirrors CFD literature
# OpenFOAM-style type aliases
const volScalarField{T,N,M} = Field{T,N,M}
const volVectorField{T,N,M} = Field{SVector{N,T},N,M}
const volTensorField{T,N,M} = Field{SMatrix{N,N,T},N,M}
const surfaceScalarField{T,N,M} = Field{T,N,M}  # Face-centered fields
const surfaceVectorField{T,N,M} = Field{SVector{N,T},N,M}

# Scalar field type aliases using mathematical symbols
# Type \phi<Tab> for φ in Julia REPL/editors
const φ{T,N,M} = Field{T,N,M}  # Generic scalar field (temperature, pressure, concentration)
const ρ{T,N,M} = Field{T,N,M}  # Density field
const scalarField{T,N,M} = Field{T,N,M}  # Unicode-friendly alias

# Vector field type aliases
const vectorField{T,N,M} = Field{SVector{N,T},N,M}  # Unicode-friendly alias
const 𝐮{T,N,M} = Field{SVector{N,T},N,M}  # Velocity field (Type \bfu<Tab>)

# Tensor field type aliases
const tensorField{T,N,M} = Field{SMatrix{N,N,T},N,M}  # Unicode-friendly alias

# Common CFD parameter symbols (these would be used as variable names)
# ν = kinematic viscosity (Type \nu<Tab>)
# μ = dynamic viscosity (Type \mu<Tab>)
# γ = diffusion coefficient, thermal conductivity (Type \gamma<Tab>)
# α = thermal diffusivity, relaxation factor (Type \alpha<Tab>)
# β = thermal expansion coefficient (Type \beta<Tab>)
# κ = thermal conductivity (Type \kappa<Tab>)
# τ = stress tensor (Type \tau<Tab>)
# σ = surface tension (Type \sigma<Tab>)
# ω = angular velocity, specific dissipation rate (Type \omega<Tab>)
# ε = turbulent dissipation rate (Type \epsilon<Tab>)
# Δt = time step (Type \Delta<Tab>t)

# Mathematical operator function stubs (to be implemented in Numerics module)
# ∇ = gradient operator (Type \nabla<Tab>)
# Δ = Laplacian operator (Type \Delta<Tab>)
# ∂ = partial derivative operator (Type \partial<Tab>)

# ============================================================================
# OpenFOAM Field Name Mapping for Unicode Integration
# ============================================================================

"""
Mapping from OpenFOAM field names to Unicode mathematical notation
"""
const UNICODE_FIELD_MAP = Dict{String, String}(
    "U" => "𝐮",           # Velocity vector (Type \\bfu<Tab>)
    "p" => "φ",           # Pressure (Type \\phi<Tab>)
    "T" => "T",           # Temperature (already Unicode-friendly)
    "k" => "k",           # Turbulent kinetic energy
    "epsilon" => "ε",     # Turbulent dissipation rate (Type \\epsilon<Tab>)
    "omega" => "ω",       # Specific dissipation rate (Type \\omega<Tab>)
    "nut" => "ν_t",       # Turbulent viscosity (Type \\nu<Tab>_t)
    "alphat" => "α_t",    # Turbulent thermal diffusivity (Type \\alpha<Tab>_t)
    "rho" => "ρ",         # Density (Type \\rho<Tab>)
    "mu" => "μ",          # Dynamic viscosity (Type \\mu<Tab>)
    "nu" => "ν",          # Kinematic viscosity (Type \\nu<Tab>)
    "alpha" => "α",       # Thermal diffusivity (Type \\alpha<Tab>)
    "gamma" => "γ",       # Heat capacity ratio (Type \\gamma<Tab>)
    "kappa" => "κ",       # Thermal conductivity (Type \\kappa<Tab>)
    "tau" => "τ",         # Stress tensor (Type \\tau<Tab>)
    "sigma" => "σ",       # Surface tension (Type \\sigma<Tab>)
    "phi" => "Φ",         # Flux field (Type \\Phi<Tab>)
    "psi" => "ψ",         # Stream function (Type \\psi<Tab>)
    "chi" => "χ",         # Scalar transport variable (Type \\chi<Tab>)
    "zeta" => "ζ",        # Vorticity (Type \\zeta<Tab>)
    "eta" => "η",         # Efficiency (Type \\eta<Tab>)
    "theta" => "θ",       # Angle, potential temperature (Type \\theta<Tab>)
    "lambda" => "λ",      # Eigenvalue, wavelength (Type \\lambda<Tab>)
    "beta" => "β"         # Thermal expansion coefficient (Type \\beta<Tab>)
)

"""
Reverse mapping from Unicode mathematical notation to OpenFOAM field names
"""
const OPENFOAM_FIELD_MAP = Dict{String, String}(
    "𝐮" => "U",
    "φ" => "p",
    "ε" => "epsilon",
    "ω" => "omega",
    "ν_t" => "nut",
    "α_t" => "alphat",
    "ρ" => "rho",
    "μ" => "mu",
    "ν" => "nu",
    "α" => "alpha",
    "γ" => "gamma",
    "κ" => "kappa",
    "τ" => "tau",
    "σ" => "sigma",
    "Φ" => "phi",
    "ψ" => "psi",
    "χ" => "chi",
    "ζ" => "zeta",
    "η" => "eta",
    "θ" => "theta",
    "λ" => "lambda",
    "β" => "beta"
)

"""
    openfoam_to_unicode(field_name::String)

Convert OpenFOAM field name to Unicode mathematical notation.

# Arguments
- `field_name`: OpenFOAM field name (e.g., "U", "p", "epsilon")

# Returns
- `String`: Unicode mathematical notation (e.g., "𝐮", "φ", "ε")

# Example
```julia
unicode_name = openfoam_to_unicode("U")        # Returns "𝐮"
unicode_name = openfoam_to_unicode("epsilon")  # Returns "ε"
```
"""
function openfoam_to_unicode(field_name::String)
    return get(UNICODE_FIELD_MAP, field_name, field_name)
end

"""
    unicode_to_openfoam(unicode_name::String)

Convert Unicode mathematical notation to OpenFOAM field name.

# Arguments
- `unicode_name`: Unicode field name (e.g., "𝐮", "φ", "ε")

# Returns
- `String`: OpenFOAM field name (e.g., "U", "p", "epsilon")

# Example
```julia
openfoam_name = unicode_to_openfoam("𝐮")  # Returns "U"
openfoam_name = unicode_to_openfoam("ε")  # Returns "epsilon"
```
"""
function unicode_to_openfoam(unicode_name::String)
    return get(OPENFOAM_FIELD_MAP, unicode_name, unicode_name)
end

end # module CFDCore
