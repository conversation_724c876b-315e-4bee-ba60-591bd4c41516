# src/Utilities/Utilities.jl
module Utilities

export <PERSON><PERSON><PERSON>ead<PERSON>, VTKWriter # ParallelIO was exported but not defined in plane.md
export read_mesh, write_solution # write_field was exported but not defined
export @timed_section
export decompose_mesh, DomainDecomposition # reconstruct_field was exported but not defined
export calculate_forces, ConvergenceMonitor, check_convergence, timed_section
export read_openfoam_mesh, read_openfoam_points, read_openfoam_faces, read_openfoam_owner
export create_unit_cube_mesh, create_periodic_mesh_2d # BlockMesh functions

# Utilities Unicode symbols for enhanced notation
export ℳ, 𝒱, ℱ  # Mesh, Volume, and Force operators
export ∫, ∮, Σ  # Integration symbols

using ..CFDCore
using ..CFDCore: AbstractMesh, ScalarField, VectorField, Field, AbstractBoundaryCondition, UnstructuredMesh, StructuredMesh, TensorField
using ..Numerics # For fvc, fvm, etc. if used by utility functions implicitly
using Printf   # For @sprintf
using StaticArrays # For SVector in calculate_forces
using LinearAlgebra: normalize, norm, cross # For vector operations

# Optional dependencies
const HAS_WRITEVTK = try
    using WriteVTK
    true
catch
    @warn "WriteVTK not available, VTK output disabled"
    false
end

const HAS_HDF5 = try
    using HDF5
    true
catch
    @warn "HDF5 not available, checkpoint I/O disabled"
    false
end

const HAS_MPI = try
    using MPI
    true
catch
    @warn "MPI not available, parallel utilities disabled"
    false
end

# include("OpenFOAMMeshReader.jl")
# using .OpenFOAMMeshReader

include("BlockMesh.jl")
using .BlockMesh

function read_openfoam_mesh(case_dir::String)::AbstractMesh
    """
    Read OpenFOAM polyMesh format from case directory.
    Expects mesh files in constant/polyMesh/
    """
    polymesh_dir = joinpath(case_dir, "constant", "polyMesh")
    
    if !isdir(polymesh_dir)
        error("polyMesh directory not found: $polymesh_dir")
    end
    
    # Read mesh files
    points = read_openfoam_points(joinpath(polymesh_dir, "points"))
    faces = read_openfoam_faces(joinpath(polymesh_dir, "faces"))
    owner = read_openfoam_owner(joinpath(polymesh_dir, "owner"))
    neighbor = read_openfoam_neighbor(joinpath(polymesh_dir, "neighbour"))
    boundary_patches = read_openfoam_boundary(joinpath(polymesh_dir, "boundary"))
    
    # Build mesh connectivity
    return build_unstructured_mesh(points, faces, owner, neighbor, boundary_patches)
end

function read_openfoam_points(filepath::String)
    """Read OpenFOAM points file - coordinates of mesh vertices"""
    if !isfile(filepath)
        error("Points file not found: $filepath")
    end
    
    lines = readlines(filepath)
    points = SVector{3,Float64}[]
    
    # Find start of point data (after header)
    start_idx = 1
    for (i, line) in enumerate(lines)
        if occursin("(", line) && !occursin("FoamFile", line)
            start_idx = i + 1
            break
        end
    end
    
    # Parse points
    for i in start_idx:length(lines)
        line = strip(lines[i])
        if startswith(line, "(") && endswith(line, ")")
            # Remove parentheses and parse coordinates
            coords_str = line[2:end-1]
            coords = parse.(Float64, split(coords_str))
            if length(coords) == 3
                push!(points, SVector{3,Float64}(coords...))
            end
        elseif line == ")"
            break
        end
    end
    
    return points
end

function read_openfoam_faces(filepath::String)
    """Read OpenFOAM faces file - face-to-vertex connectivity"""
    if !isfile(filepath)
        error("Faces file not found: $filepath")
    end
    
    lines = readlines(filepath)
    faces = Vector{Int}[]
    
    # Find start of face data (look for standalone opening parenthesis)
    start_idx = 1
    for (i, line) in enumerate(lines)
        if strip(line) == "("
            start_idx = i + 1
            break
        end
    end
    
    # Parse faces
    for i in start_idx:length(lines)
        line = strip(lines[i])
        if occursin("(", line) && occursin(")", line)
            # Extract face vertices - format: 4(1 12 133 122)
            face_match = match(r"(\d+)\((.+?)\)", line)
            if face_match !== nothing
                vertices_str = face_match.captures[2]  # Second capture group has the vertices
                vertices = parse.(Int, split(vertices_str)) .+ 1  # Convert to 1-based indexing
                push!(faces, vertices)
            end
        elseif line == ")"
            break
        end
    end
    
    return faces
end

function read_openfoam_owner(filepath::String)
    """Read OpenFOAM owner file - face owner cells"""
    if !isfile(filepath)
        error("Owner file not found: $filepath")
    end
    
    lines = readlines(filepath)
    owners = Int[]
    
    # Find start of data
    start_idx = 1
    for (i, line) in enumerate(lines)
        if occursin("(", line) && !occursin("FoamFile", line)
            start_idx = i + 1
            break
        end
    end
    
    # Parse owner indices
    for i in start_idx:length(lines)
        line = strip(lines[i])
        if !isempty(line) && all(isdigit, line)
            push!(owners, parse(Int, line) + 1)  # Convert to 1-based indexing
        elseif line == ")"
            break
        end
    end
    
    return owners
end

function read_openfoam_neighbor(filepath::String)
    """Read OpenFOAM neighbour file - face neighbor cells"""
    if !isfile(filepath)
        # Neighbor file may not exist for some meshes
        return Int[]
    end
    
    lines = readlines(filepath)
    neighbors = Int[]
    
    # Find start of data
    start_idx = 1
    for (i, line) in enumerate(lines)
        if occursin("(", line) && !occursin("FoamFile", line)
            start_idx = i + 1
            break
        end
    end
    
    # Parse neighbor indices
    for i in start_idx:length(lines)
        line = strip(lines[i])
        if !isempty(line) && all(isdigit, line)
            push!(neighbors, parse(Int, line) + 1)  # Convert to 1-based indexing
        elseif line == ")"
            break
        end
    end
    
    return neighbors
end

function read_openfoam_boundary(filepath::String)
    """Read OpenFOAM boundary file - boundary patch definitions"""
    if !isfile(filepath)
        error("Boundary file not found: $filepath")
    end
    
    boundary_patches = Dict{String, Vector{Int}}()
    
    # For now, return empty boundary dict - would need full parser
    # This is a simplified version
    println("Warning: OpenFOAM boundary parsing not fully implemented")
    
    return boundary_patches
end

function build_unstructured_mesh(points::Vector{SVector{3,Float64}}, 
                                face_vertices::Vector{Vector{Int}},
                                owners::Vector{Int}, 
                                neighbors::Vector{Int},
                                boundary_patches::Dict{String, Vector{Int}})
    """
    Build UnstructuredMesh from OpenFOAM mesh data
    """
    # Create nodes
    nodes = [Node{Float64,3}(i, points[i], false) for i in 1:length(points)]
    
    # Determine number of cells (max of owner/neighbor indices)
    num_cells = max(maximum(owners), isempty(neighbors) ? 0 : maximum(neighbors))
    
    # Create basic face and cell structures (simplified)
    faces = Face{Float64,3}[]
    cells = Cell{Float64,3}[]
    
    # Build faces
    for (face_idx, vertices) in enumerate(face_vertices)
        owner_idx = owners[face_idx]
        neighbor_idx = length(neighbors) >= face_idx ? neighbors[face_idx] : 0
        
        # Calculate face center (simplified)
        face_center = sum(points[v] for v in vertices) / length(vertices)
        
        # Calculate face normal (simplified for triangular/quad faces)
        if length(vertices) >= 3
            v1 = points[vertices[2]] - points[vertices[1]]
            v2 = points[vertices[3]] - points[vertices[1]]
            normal = normalize(cross(v1, v2))
        else
            normal = SVector(1.0, 0.0, 0.0)  # Default normal
        end
        
        # Calculate face area (simplified)
        area = norm(cross(v1, v2)) / 2.0
        
        is_boundary = (neighbor_idx == 0)
        
        face = Face{Float64,3}(
            face_idx,
            vertices,          # nodes
            face_center,       # center
            area,              # area
            normal,            # normal
            owner_idx,         # owner
            neighbor_idx,      # neighbor
            is_boundary        # boundary
        )
        
        push!(faces, face)
    end
    
    # Create basic cells (simplified - would need proper volume calculation)
    for cell_idx in 1:num_cells
        # Find cell center (average of face centers)
        cell_faces = [f for f in faces if f.owner == cell_idx || f.neighbor == cell_idx]
        if !isempty(cell_faces)
            center = sum(f.center for f in cell_faces) / length(cell_faces)
            volume = 1.0  # Placeholder - would need proper calculation
        else
            center = SVector(0.0, 0.0, 0.0)
            volume = 1.0
        end
        
        cell = Cell{Float64,3}(
            cell_idx,
            Int[],  # nodes - would need to populate from faces
            Int[],  # face_indices - would need to populate
            center,
            volume
        )
        
        push!(cells, cell)
    end
    
    # Build connectivity (simplified)
    cell_to_cell = [Int[] for _ in 1:num_cells]
    face_to_cell = [(f.owner, f.neighbor) for f in faces]
    
    # Calculate bounding box
    min_coords = minimum(points)
    max_coords = maximum(points)
    bbox = (min_coords, max_coords)
    
    # Create mesh
    mesh = UnstructuredMesh{Float64,3}(
        nodes,
        faces, 
        cells,
        boundary_patches,
        cell_to_cell,
        face_to_cell,
        bbox
    )
    
    return mesh
end

function read_gmsh_mesh(filename::String)::AbstractMesh
    """
    Read Gmsh mesh file (.msh format)
    Basic implementation for version 2.2 ASCII format
    """
    if !isfile(filename)
        error("Gmsh mesh file not found: $filename")
    end
    
    try
        lines = readlines(filename)
        
        # Parse nodes
        nodes = Vector{SVector{3,Float64}}()
        node_map = Dict{Int, Int}()
        
        # Parse elements
        cells = Vector{Cell{Float64,3}}()
        faces = Vector{Face{Float64,3}}()
        
        i = 1
        while i <= length(lines)
            line = strip(lines[i])
            
            if line == "\$Nodes"
                i += 1
                n_nodes = parse(Int, strip(lines[i]))
                i += 1
                
                for j in 1:n_nodes
                    parts = split(strip(lines[i]))
                    node_id = parse(Int, parts[1])
                    x = parse(Float64, parts[2])
                    y = parse(Float64, parts[3])
                    z = parse(Float64, parts[4])
                    
                    push!(nodes, SVector{3,Float64}(x, y, z))
                    node_map[node_id] = length(nodes)
                    i += 1
                end
                
            elseif line == "\$Elements"
                i += 1
                n_elements = parse(Int, strip(lines[i]))
                i += 1
                
                for j in 1:n_elements
                    parts = split(strip(lines[i]))
                    elem_id = parse(Int, parts[1])
                    elem_type = parse(Int, parts[2])
                    
                    # Skip tag information
                    n_tags = parse(Int, parts[3])
                    node_start = 4 + n_tags
                    
                    # Extract node IDs
                    elem_nodes = [parse(Int, parts[k]) for k in node_start:length(parts)]
                    
                    # Convert to 1-based indexing
                    elem_nodes_mapped = [node_map[nid] for nid in elem_nodes]
                    
                    # Create cell based on element type
                    if elem_type == 5 && length(elem_nodes_mapped) == 8  # Hexahedron
                        # Calculate cell center and volume
                        cell_coords = [nodes[nid] for nid in elem_nodes_mapped]
                        center = sum(cell_coords) / length(cell_coords)
                        volume = calculate_hex_volume_simple(cell_coords)
                        
                        cell = Cell{Float64,3}(
                            length(cells) + 1,
                            elem_nodes_mapped .- 1,  # Convert to 0-based for consistency
                            Int[],  # Face indices - would need face generation
                            center,
                            volume
                        )
                        push!(cells, cell)
                    end
                    
                    i += 1
                end
            else
                i += 1
            end
        end
        
        # Create nodes with proper structure
        mesh_nodes = [Node{Float64,3}(i-1, nodes[i], false) for i in 1:length(nodes)]
        
        # Create basic mesh structure
        boundaries = Dict{String, Vector{Int}}()
        cell_to_cell = [Int[] for _ in 1:length(cells)]
        face_to_cell = Tuple{Int,Int}[]
        bbox = (minimum(nodes), maximum(nodes))
        
        mesh = UnstructuredMesh{Float64,3}(
            mesh_nodes,
            faces,  # Empty for now - would need face generation
            cells,
            boundaries,
            cell_to_cell,
            face_to_cell,
            bbox
        )
        
        @info "Successfully read Gmsh mesh: $(length(cells)) cells, $(length(nodes)) nodes"
        return mesh
        
    catch e
        @warn "Failed to read Gmsh mesh: $e"
        error("GMSH mesh reading failed: $e")
    end
end

function calculate_hex_volume_simple(coords::Vector{SVector{3,T}}) where T
    """
    Simple hexahedron volume calculation
    """
    if length(coords) != 8
        return 1.0  # Fallback volume
    end
    
    # Simple approximation: assume rectangular hexahedron
    dx = norm(coords[2] - coords[1])
    dy = norm(coords[4] - coords[1])
    dz = norm(coords[5] - coords[1])
    
    return dx * dy * dz
end

function mesh_to_vtk(mesh::AbstractMesh)
    """
    Convert mesh to VTK-compatible format for visualization
    """
    if !HAS_WRITEVTK
        @warn "WriteVTK not available, returning basic mesh data"
        # Return basic data that can still be used for simple analysis
        if mesh isa UnstructuredMesh
            points_vtk = hcat([node.coords for node in mesh.nodes]...)
            cell_data = [(cell.id, cell.nodes) for cell in mesh.cells]
            return points_vtk, cell_data
        end
        return zeros(Float64, 3, 1), []
    end
    
    try
        if mesh isa UnstructuredMesh
            # Convert points to VTK format (3×N matrix)
            points_vtk = hcat([node.coords for node in mesh.nodes]...)
            
            # Convert cells to VTK format
            vtk_cells = Vector{Tuple{Vector{Int}, Int}}()
            
            for cell in mesh.cells
                # Determine VTK cell type based on number of nodes
                n_nodes = length(cell.nodes)
                vtk_cell_type = if n_nodes == 8
                    12  # VTK_HEXAHEDRON
                elseif n_nodes == 4
                    10  # VTK_TETRA
                elseif n_nodes == 6
                    13  # VTK_WEDGE
                else
                    1   # VTK_VERTEX (fallback)
                end
                
                # VTK uses 0-based indexing, adjust if needed
                node_indices = [node_id + 1 for node_id in cell.nodes]  # Convert to 1-based for Julia
                push!(vtk_cells, (node_indices, vtk_cell_type))
            end
            
            return points_vtk, vtk_cells
            
        elseif mesh isa StructuredMesh
            # Handle structured mesh
            dims = mesh.dims
            origin = mesh.origin
            spacing = mesh.spacing
            
            # Generate structured grid points
            points = Vector{SVector{3,Float64}}()
            for k in 0:dims[3], j in 0:dims[2], i in 0:dims[1]
                x = origin[1] + i * spacing[1]
                y = origin[2] + j * spacing[2]
                z = origin[3] + k * spacing[3]
                push!(points, SVector{3,Float64}(x, y, z))
            end
            
            points_vtk = hcat(points...)
            
            # Generate structured cells
            vtk_cells = Vector{Tuple{Vector{Int}, Int}}()
            cell_id = 1
            for k in 0:(dims[3]-1), j in 0:(dims[2]-1), i in 0:(dims[1]-1)
                # Calculate node indices for hexahedron
                n0 = i + j*(dims[1]+1) + k*(dims[1]+1)*(dims[2]+1)
                n1 = n0 + 1
                n2 = n0 + (dims[1]+1)
                n3 = n2 + 1
                n4 = n0 + (dims[1]+1)*(dims[2]+1)
                n5 = n4 + 1
                n6 = n4 + (dims[1]+1)
                n7 = n6 + 1
                
                # VTK hexahedron node ordering (1-based for Julia)
                nodes = [n0+1, n1+1, n3+1, n2+1, n4+1, n5+1, n7+1, n6+1]
                push!(vtk_cells, (nodes, 12))  # VTK_HEXAHEDRON
                cell_id += 1
            end
            
            return points_vtk, vtk_cells
            
        else
            error("Unsupported mesh type for mesh_to_vtk: $(typeof(mesh))")
        end
        
    catch e
        @warn "VTK conversion failed: $e"
        # Return simplified data as fallback
        if mesh isa UnstructuredMesh && !isempty(mesh.nodes)
            points_vtk = hcat([node.coords for node in mesh.nodes]...)
            cell_data = [(cell.id, cell.nodes) for cell in mesh.cells]
            return points_vtk, cell_data
        end
        return zeros(Float64, 3, 1), []
    end
end

function geometric_decomposition(mesh::AbstractMesh, ndomains::Int)::Vector{AbstractMesh}
    """
    Simple geometric decomposition of mesh into domains
    """
    if ndomains <= 1
        return [mesh]
    end
    
    try
        if mesh isa UnstructuredMesh
            # Simple geometric decomposition by coordinate bounds
            cells = mesh.cells
            if isempty(cells)
                return [mesh]
            end
            
            # Find coordinate bounds
            x_coords = [cell.center[1] for cell in cells]
            x_min, x_max = minimum(x_coords), maximum(x_coords)
            
            # Divide domain geometrically
            domains = Vector{AbstractMesh}()
            cells_per_domain = length(cells) ÷ ndomains
            
            for domain_id in 1:ndomains
                # Define domain bounds
                x_start = x_min + (domain_id - 1) * (x_max - x_min) / ndomains
                x_end = x_min + domain_id * (x_max - x_min) / ndomains
                
                # Select cells in this domain
                domain_cells = filter(cell -> x_start <= cell.center[1] <= x_end, cells)
                
                if !isempty(domain_cells)
                    # Create submesh (simplified - just change cell list)
                    # In practice would need proper node/face management
                    domain_mesh = UnstructuredMesh{Float64,3}(
                        mesh.nodes,  # Shared nodes for simplicity
                        mesh.faces,  # Shared faces for simplicity
                        domain_cells,
                        mesh.boundaries,
                        mesh.cell_to_cell,
                        mesh.face_to_cell,
                        mesh.bbox
                    )
                    push!(domains, domain_mesh)
                end
            end
            
            # If no domains created, return original mesh
            if isempty(domains)
                return [mesh]
            end
            
            @info "Geometric decomposition: $(length(domains)) domains created"
            return domains
            
        else
            @warn "Geometric decomposition not implemented for $(typeof(mesh))"
            return [mesh]
        end
        
    catch e
        @warn "Geometric decomposition failed: $e"
        return [mesh]
    end
end

function graph_decomposition(mesh::AbstractMesh, ndomains::Int, method::Symbol)::Vector{AbstractMesh}
    """
    Graph-based mesh decomposition (simplified implementation)
    """
    if ndomains <= 1
        return [mesh]
    end
    
    @info "Graph decomposition using $method method (simplified implementation)"
    
    try
        if mesh isa UnstructuredMesh
            # Simple graph partitioning based on connectivity
            cells = mesh.cells
            if isempty(cells)
                return [mesh]
            end
            
            # Create simple partitioning based on cell connectivity
            # This is a simplified version - real implementation would use METIS/SCOTCH
            n_cells = length(cells)
            cells_per_domain = n_cells ÷ ndomains
            
            domains = Vector{AbstractMesh}()
            
            for domain_id in 1:ndomains
                start_idx = (domain_id - 1) * cells_per_domain + 1
                end_idx = domain_id == ndomains ? n_cells : domain_id * cells_per_domain
                
                domain_cells = cells[start_idx:end_idx]
                
                # Create domain mesh (simplified)
                domain_mesh = UnstructuredMesh{Float64,3}(
                    mesh.nodes,  # Shared nodes for simplicity
                    mesh.faces,  # Shared faces for simplicity
                    domain_cells,
                    mesh.boundaries,
                    mesh.cell_to_cell,
                    mesh.face_to_cell,
                    mesh.bbox
                )
                push!(domains, domain_mesh)
            end
            
            @info "Graph decomposition ($method): $(length(domains)) domains created"
            return domains
            
        else
            @warn "Graph decomposition not implemented for $(typeof(mesh))"
            return [mesh]
        end
        
    catch e
        @warn "Graph decomposition failed: $e"
        return [mesh]
    end
end
# --- End Placeholder functions ---

# Mesh I/O
struct MeshReader
    format::Symbol  # :openfoam, :gmsh, :cgns, :fluent
end

function read_mesh(reader::MeshReader, filename::String)::AbstractMesh
    if reader.format == :openfoam
        return read_openfoam_mesh(filename) # filename is case_dir for OpenFOAM
    elseif reader.format == :gmsh
        return read_gmsh_mesh(filename)
    else
        error("Unsupported mesh format: $(reader.format)")
    end
end

# Solution I/O
struct VTKWriter
    basename::String
    append::Bool # Default to false, true if trying to append to existing multi-timestep file if supported
    VTKWriter(basename::String, append=false) = new(basename, append)
end

function write_solution(writer::VTKWriter, mesh::AbstractMesh,
                       fields::Dict{Symbol,<:Field}, 
                       timestep::Int, time::Float64)
    if !HAS_WRITEVTK
        @warn "WriteVTK not available, skipping VTK output"
        return
    end
    
    # Ensure basename does not include extensions, they will be added.
    filename_noext = writer.basename
    vtk_filename = @sprintf("%s_%06d", filename_noext, timestep)
    
    println("VTK output would be written to $(vtk_filename) (WriteVTK integration needed)")
    println("Fields to export: $(keys(fields))")
end


# Parallel utilities
struct DomainDecomposition
    method::Symbol  # :metis, :scotch, :simple
    ndomains::Int
end

function decompose_mesh(mesh::AbstractMesh, decomp::DomainDecomposition)::Vector{AbstractMesh}
    if decomp.method == :simple
        return geometric_decomposition(mesh, decomp.ndomains)
    else # :metis, :scotch, etc.
        return graph_decomposition(mesh, decomp.ndomains, decomp.method)
    end
end

# HDF5 I/O for large datasets
function write_checkpoint(filename::String, fields::Dict{Symbol,<:Field}, metadata::Dict{Symbol,Any})
    if !HAS_HDF5
        @warn "HDF5 not available, skipping checkpoint write"
        return
    end
    
    println("Checkpoint would be written to $filename (HDF5 integration needed)")
    println("Fields: $(keys(fields))")
    println("Metadata: $(keys(metadata))")
end

function read_checkpoint(filename::String, mesh::AbstractMesh)::Tuple{Dict{Symbol,Field}, Dict{Symbol,Any}}
    if !HAS_HDF5
        @warn "HDF5 not available, returning empty checkpoint data"
        return Dict{Symbol,Field}(), Dict{Symbol,Any}()
    end
    
    println("Would read checkpoint from $filename (HDF5 integration needed)")
    return Dict{Symbol,Field}(), Dict{Symbol,Any}()
end

# Post-processing utilities
function calculate_forces(p_field::ScalarField{TF,NF,MeshT}, 
                          U_field::VectorField{TF_Vec, NF_Vec, MeshT_Vec}, 
                          patch_name::String, 
                          ρ::Float64, ν::Float64, 
                          reference_point::SVector{NF_Vec,TF_Vec} = zero(SVector{NF_Vec,TF_Vec})) where {TF,NF,MeshT, TF_Vec,NF_Vec,MeshT_Vec}
    
    mesh = p_field.mesh # Assuming p_field and U_field are on the same mesh
    N = NF_Vec # Spatial dimension from VectorField SVector component count

    total_force_on_surface = zero(SVector{N, promote_type(TF, TF_Vec, Float64)})
    total_moment_on_surface = zero(SVector{N, promote_type(TF, TF_Vec, Float64)})
    
    if !haskey(mesh.boundaries, patch_name)
        error("Patch name '$patch_name' not found in mesh boundaries.")
    end
    face_indices_on_patch = mesh.boundaries[patch_name]
    
    for face_idx in face_indices_on_patch
        face = mesh.faces[face_idx]
        
        # Pressure at face (from owner cell - a common simplification)
        p_val = p_field.data[face.owner]
        
        # Pressure force on surface element: Fp_elem = p_val * face.normal * face.area
        # This force is exerted BY the fluid ON the surface if normal points out of fluid.
        # If face.normal points from owner to neighbor (or outside for boundary), this is correct.
        fp_element = p_val * face.normal * face.area
        total_force_on_surface += fp_element
        
        # Viscous force on surface element: Fv_elem = (τ_wall ⋅ n) * face.area
        # τ_wall = μ (∇U + (∇U)ᵀ) where μ = ρν
        # Calculating ∇U at the wall face is complex. Simplified/Placeholder in plane.md.
        # For a no-slip wall, if U_wall = 0, then ∇U has non-zero wall-normal component.
        # gradU_wall_approx = (U_field.data[face.owner] - zero(SVector{N,TF_Vec})) / (distance_to_wall_center) 
        # This is a very rough approximation of wall shear stress related terms.
        # For now, viscous force is omitted as per plane.md's simplified approach.
        
        # Moment calculation: M_elem = r × F_elem_total
        r_vec = face.center - reference_point # Vector from ref point to element
        total_moment_on_surface += cross(r_vec, fp_element) # Add cross(r_vec, fv_element) if viscous force is included
    end
    
    return total_force_on_surface, total_moment_on_surface
end

# Monitoring and convergence
mutable struct ConvergenceMonitor
    fields_to_monitor::Vector{Symbol}
    tolerances::Dict{Symbol,Float64}
    residuals_history::Dict{Symbol,Vector{Float64}} # Stores history of residuals
    current_iteration::Int

    function ConvergenceMonitor(fields::Vector{Symbol}, tols::Dict{Symbol,Float64})
        res_hist = Dict{Symbol,Vector{Float64}}(f => Float64[] for f in fields)
        new(fields, tols, res_hist, 0)
    end
end

function check_convergence(monitor::ConvergenceMonitor, current_iter_residuals::Dict{Symbol,Float64})::Bool
    all_fields_converged = true
    
    for field_symbol in monitor.fields_to_monitor
        if !haskey(current_iter_residuals, field_symbol)
            println("Warning: Residual for monitored field '$field_symbol' not found in current iteration residuals.")
            all_fields_converged = false # Or handle as error / non-convergence
            continue
        end
        
        res_val = current_iter_residuals[field_symbol]
        push!(monitor.residuals_history[field_symbol], res_val)
        
        if res_val > monitor.tolerances[field_symbol]
            all_fields_converged = false
        end
    end
    
    monitor.current_iteration += 1
    return all_fields_converged
end

# Performance profiling
macro timed_section(name_expr, code_block)
    # name_expr should be a string literal or a symbol that evaluates to a string
    name_str = string(name_expr) 
    quote
        local t_start = time_ns()
        local result = $(esc(code_block))
        local t_end = time_ns()
        println("Timed Section [", $name_str, "]: ", round((t_end - t_start) / 1e6, digits=3), " ms")
        result # Return the value of the code_block
    end
end

# BlockMesh convenience functions for validation framework

"""
Create a unit cube mesh using blockMesh
"""
function create_unit_cube_mesh(nx::Int, ny::Int, nz::Int)
    block_dict = BlockMesh.create_unit_cube_dict(nx, ny, nz)
    return BlockMesh.generate_mesh(block_dict)
end

"""
Create a periodic mesh in 2D (for validation purposes)
For now, creates a simple rectangular mesh
"""
function create_periodic_mesh_2d(nx::Int, ny::Int, lx::Float64, ly::Float64)
    @info "Creating 2D periodic mesh: $(nx)×$(ny), domain: $(lx)×$(ly)"
    
    # Create a 2D mesh by making a thin 3D mesh
    T = Float64
    
    vertices = [
        BlockMesh.Vertex{T}(0, SVector{3,T}(0, 0, 0)),
        BlockMesh.Vertex{T}(1, SVector{3,T}(lx, 0, 0)),
        BlockMesh.Vertex{T}(2, SVector{3,T}(lx, ly, 0)),
        BlockMesh.Vertex{T}(3, SVector{3,T}(0, ly, 0)),
        BlockMesh.Vertex{T}(4, SVector{3,T}(0, 0, 0.1)),
        BlockMesh.Vertex{T}(5, SVector{3,T}(lx, 0, 0.1)),
        BlockMesh.Vertex{T}(6, SVector{3,T}(lx, ly, 0.1)),
        BlockMesh.Vertex{T}(7, SVector{3,T}(0, ly, 0.1))
    ]
    
    blocks = [
        BlockMesh.Block{T}(
            1,
            [0, 1, 2, 3, 4, 5, 6, 7],
            SVector{3,Int}(nx, ny, 1),  # 1 cell in z-direction
            SVector{3,T}(1.0, 1.0, 1.0)
        )
    ]
    
    edges = BlockMesh.AbstractEdge[]
    
    boundaries = [
        BlockMesh.BoundaryPatch("left", "cyclic", [[0, 4, 7, 3]]),
        BlockMesh.BoundaryPatch("right", "cyclic", [[1, 2, 6, 5]]),
        BlockMesh.BoundaryPatch("bottom", "cyclic", [[0, 1, 5, 4]]),
        BlockMesh.BoundaryPatch("top", "cyclic", [[3, 7, 6, 2]]),
        BlockMesh.BoundaryPatch("front", "empty", [[0, 3, 2, 1]]),
        BlockMesh.BoundaryPatch("back", "empty", [[4, 5, 6, 7]])
    ]
    
    block_dict = BlockMesh.BlockMeshDict{T}(1.0, vertices, blocks, edges, boundaries, nothing)
    return BlockMesh.generate_mesh(block_dict)
end

# ============================================================================
# Utilities Unicode Symbols for Enhanced CFD Notation
# ============================================================================

"""
Utilities Unicode symbols for enhanced CFD notation.
These provide intuitive mathematical notation for mesh and post-processing operations.
"""

# Mesh and geometry operators
"""
    ℳ(mesh_params...)

Mesh generation operator using Unicode notation.
"""
function ℳ(mesh_type::Symbol, params...)
    # Mesh generation operator implementation
    if mesh_type == :unit_cube
        # Generate unit cube mesh
        if length(params) >= 1
            n = params[1]
            return create_unit_cube_mesh(n, n, n)
        else
            return create_unit_cube_mesh(10, 10, 10)
        end
    elseif mesh_type == :periodic_2d
        # Generate 2D periodic mesh
        if length(params) >= 2
            nx, ny = params[1], params[2]
            return create_periodic_mesh_2d(nx, ny)
        else
            return create_periodic_mesh_2d(20, 20)
        end
    elseif mesh_type == :cavity
        # Generate lid-driven cavity mesh
        if length(params) >= 1
            n = params[1]
            return BlockMesh.create_cavity_mesh(n)
        else
            return BlockMesh.create_cavity_mesh(20)
        end
    elseif mesh_type == :channel
        # Generate channel mesh
        if length(params) >= 3
            nx, ny, nz = params[1], params[2], params[3]
            return BlockMesh.create_channel_mesh(nx, ny, nz)
        else
            return BlockMesh.create_channel_mesh(50, 20, 10)
        end
    else
        @warn "Unsupported mesh type: $mesh_type, generating default unit cube"
        return create_unit_cube_mesh(10, 10, 10)
    end
end

"""
    𝒱(mesh)

Volume calculation operator. Computes total volume of mesh.
"""
function 𝒱(mesh::AbstractMesh)
    total_volume = 0.0
    for cell in mesh.cells
        total_volume += cell.volume
    end
    return total_volume
end

"""
    ℱ(field, patch_names)

Force calculation operator using Unicode notation.
"""
function ℱ(field::Union{ScalarField, VectorField}, patch_names::Vector{String})
    return calculate_forces(field, patch_names)
end

# Integration symbols
"""
    ∫(integrand, domain)

Volume integral operator using Unicode notation.
"""
function ∫(integrand::Union{ScalarField, VectorField}, domain::AbstractMesh)
    # Volume integration implementation
    if isa(integrand, ScalarField)
        # Scalar volume integral: ∫ φ dV
        total_integral = 0.0
        for (i, cell) in enumerate(domain.cells)
            if i <= length(integrand.data)
                total_integral += integrand.data[i] * cell.volume
            end
        end
        return total_integral
    else
        # Vector volume integral: ∫ U dV
        total_integral = SVector(0.0, 0.0, 0.0)
        for (i, cell) in enumerate(domain.cells)
            if i <= length(integrand.data)
                total_integral += integrand.data[i] * cell.volume
            end
        end
        return total_integral
    end
end

"""
    ∮(integrand, boundary)

Surface integral operator using Unicode notation.
"""
function ∮(integrand::Union{ScalarField, VectorField}, boundary::Vector{String})
    # Surface integration implementation
    mesh = integrand.mesh
    
    if isa(integrand, ScalarField)
        # Scalar surface integral: ∮ φ dS
        total_integral = 0.0
        
        # Integrate over specified boundary patches
        for patch_name in boundary
            if haskey(mesh.boundaries, patch_name)
                face_ids = mesh.boundaries[patch_name]
                for face_id in face_ids
                    face = mesh.faces[face_id]
                    # Use cell center value at boundary (simplified)
                    owner_id = face.owner
                    if owner_id <= length(integrand.data)
                        total_integral += integrand.data[owner_id] * face.area
                    end
                end
            end
        end
        return total_integral
    else
        # Vector surface integral: ∮ U⋅n dS (flux through surface)
        total_flux = 0.0
        
        for patch_name in boundary
            if haskey(mesh.boundaries, patch_name)
                face_ids = mesh.boundaries[patch_name]
                for face_id in face_ids
                    face = mesh.faces[face_id]
                    owner_id = face.owner
                    if owner_id <= length(integrand.data)
                        # Dot product with face normal
                        flux = dot(integrand.data[owner_id], face.normal) * face.area
                        total_flux += flux
                    end
                end
            end
        end
        return total_flux
    end
end

"""
    Σ(field)

Summation operator for field values.
"""
function Σ(field::Union{ScalarField, VectorField})
    if field isa ScalarField
        return sum(field.data)
    else
        return sum(norm(v) for v in field.data)
    end
end

end # module Utilities