# src/Numerics/Numerics.jl
module Numerics

export AbstractScheme, AbstractGradientScheme, AbstractDivergenceScheme
export AbstractInterpolationScheme, AbstractLaplacianScheme, AbstractConvectionScheme
export GaussGradient, LeastSquaresGradient, GreenGaussGradient, GaussDivergence
export LinearInterpolation, UpwindInterpolation, CentralDifferencing
export DefaultFvcLaplacian, GaussLaplacian, StandardConvectionScheme
export fvc, fvm

# Mathematical Unicode operators for enhanced CFD notation
export ∇, Δ, ∂  # grad, laplacian, partial derivative
export 𝛻, 𝜕     # alternative gradient and partial symbols

using ..CFDCore # To access CFD.CFDCore types like Field, ScalarField, AbstractMesh etc.
using LinearAlgebra
using SparseArrays
using StaticArrays # For SVector

# Abstract scheme types
abstract type AbstractScheme end
abstract type AbstractGradientScheme <: AbstractScheme end
abstract type AbstractDivergenceScheme <: AbstractScheme end
abstract type AbstractInterpolationScheme <: AbstractScheme end
abstract type AbstractLaplacianScheme <: AbstractScheme end
abstract type AbstractConvectionScheme <: AbstractScheme end

# Gradient schemes
struct GaussGradient <: AbstractGradientScheme end
struct GaussDivergence <: AbstractDivergenceScheme end
struct LeastSquaresGradient <: AbstractGradientScheme end
struct GreenGaussGradient <: AbstractGradientScheme end

# Interpolation schemes
struct LinearInterpolation <: AbstractInterpolationScheme end
struct UpwindInterpolation <: AbstractInterpolationScheme end
struct CentralDifferencing <: AbstractInterpolationScheme end

# Laplacian schemes (add concrete types as needed)
struct DefaultFvcLaplacian <: AbstractLaplacianScheme end # Placeholder for fvc.laplacian
struct GaussLaplacian <: AbstractLaplacianScheme end

# Convection schemes
struct StandardConvectionScheme{I<:AbstractInterpolationScheme} <: AbstractConvectionScheme
    interpolation_scheme::I # e.g., UpwindInterpolation or CentralDifferencing
end

include("fvc.jl")

include("fvm.jl")

# ============================================================================
# Mathematical Unicode Operators for Enhanced CFD Notation
# ============================================================================

"""
Mathematical Unicode operators that provide intuitive notation for CFD operations.
These operators mirror mathematical literature and make code more readable.

Examples:
- ∇φ instead of grad(φ)
- ∇⋅𝐮 instead of div(𝐮)  
- ∇²φ or Δφ instead of laplacian(φ)
"""

# Gradient operator: ∇ (Type \nabla<Tab>)
"""
    ∇(field::ScalarField; scheme=GaussGradient(), current_time=0.0)

Gradient operator using Unicode notation. Equivalent to `fvc.grad(field, scheme)`.

# Example
```julia
φ = ScalarField(:pressure, mesh, data, bcs)
velocity_field = ∇φ  # Returns VectorField
```
"""
const ∇ = fvc.grad

# Alternative gradient symbol: 𝛻 (Type \bfnabla<Tab>)
const 𝛻 = fvc.grad

# Note: Compound operators like ∇⋅ and ∇× cause Julia parsing issues
# Use the convenience functions instead: divergence() and gradient()

# Laplacian operator: Δ (Type \Delta<Tab>)
"""
    Δ(field::ScalarField; γ=1.0, scheme=DefaultFvcLaplacian(), current_time=0.0)

Laplacian operator using Unicode notation. Equivalent to `fvc.laplacian(γ, field, scheme)`.

# Example
```julia
φ = ScalarField(:temperature, mesh, data, bcs)
laplacian_phi = Δφ  # Returns ScalarField
```
"""
function Δ(field::CFDCore.ScalarField{TF,NF,M}; γ::Number=1.0, scheme::AbstractLaplacianScheme=DefaultFvcLaplacian(), current_time::Float64=0.0) where {TF,NF,M}
    return fvc.laplacian(γ, field, scheme, current_time=current_time)
end

# Alternative Laplacian notation available as: laplacian() function

# Partial derivative operator: ∂ (Type \partial<Tab>)
# This is a placeholder for partial derivatives that would be implemented
# in specific time/space discretization schemes
"""
    ∂(field::Field, direction::Symbol)

Partial derivative operator placeholder. 
To be implemented for specific discretization schemes.

# Example
```julia
∂φ_∂t = ∂(φ, :time)    # Time derivative
∂φ_∂x = ∂(φ, :x)       # Spatial derivative in x-direction
```
"""
function ∂(field::CFDCore.AbstractField, direction::Symbol)
    error("Partial derivative operator ∂ not yet implemented for direction $direction")
end

# Alternative partial symbol: 𝜕 (Type \bfpartial<Tab>)
const 𝜕 = ∂

# Curl operator: Available through curl() function when implemented

# ============================================================================
# Convenience Functions for Common CFD Operations
# ============================================================================

"""
    laplacian(field; γ=1.0, scheme=DefaultFvcLaplacian())

Convenience function for Laplacian operator with default parameters.
"""
function laplacian(field::CFDCore.ScalarField; γ::Number=1.0, scheme::AbstractLaplacianScheme=DefaultFvcLaplacian(), current_time::Float64=0.0)
    return fvc.laplacian(γ, field, scheme, current_time=current_time)
end

function laplacian(field::CFDCore.Field; γ::Number=1.0, scheme::AbstractLaplacianScheme=DefaultFvcLaplacian(), current_time::Float64=0.0)
    return fvc.laplacian(γ, field, scheme, current_time=current_time)
end

"""
    gradient(field; scheme=GaussGradient())

Convenience function for gradient operator with default parameters.
"""
function gradient(field::CFDCore.ScalarField; scheme::AbstractGradientScheme=GaussGradient(), current_time::Float64=0.0)
    return fvc.grad(field, scheme, current_time=current_time)
end

function gradient(field::CFDCore.Field; scheme::AbstractGradientScheme=GaussGradient(), current_time::Float64=0.0)
    return fvc.grad(field, scheme, current_time=current_time)
end

"""
    divergence(field; scheme=CentralDifferencing())

Convenience function for divergence operator with default parameters.
"""
function divergence(field::Union{CFDCore.VectorField, CFDCore.Field}; scheme::AbstractDivergenceScheme=CentralDifferencing())
    return fvc.div(field, scheme)
end

end # module Numerics
