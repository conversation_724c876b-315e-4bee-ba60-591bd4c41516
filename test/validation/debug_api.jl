# Debug CFD.jl API to understand the actual interface

using CFD

println("🔍 Debugging CFD.jl API...")

# Test what solve returns
println("\n1. Testing CFD.solve return value:")
result = CFD.solve("test/fixtures/complete_test", solver=:icoFoam, time=0.05, dt=0.01)
println("Result type: $(typeof(result))")
println("Result keys: $(keys(result))")
println("Result: $result")

# Test solver listing
println("\n2. Testing solver listing:")
try
    solvers = CFD.list_available_solvers()
    println("Solvers type: $(typeof(solvers))")
    println("Number of solvers: $(length(solvers))")
    if length(solvers) > 0
        println("First solver: $(solvers[1])")
    end
catch e
    println("Error listing solvers: $e")
end

# Test what methods are available
println("\n3. Available CFD methods:")
methods_list = names(CFD)
for name in methods_list
    if !startswith(string(name), "_")  # Skip private methods
        println("  - $name")
    end
end

# Test some specific functionality
println("\n4. Testing specific functionality:")
try
    println("CFD.solve function: $(hasmethod(CFD.solve, Tuple{String}))")
    println("Available solve methods:")
    for m in methods(CFD.solve)
        println("  - $m")
    end
catch e
    println("Error checking solve methods: $e")
end